# cardType=5 使用说明

## 功能描述

当 `cardType=5` 时，系统支持通过一个处方号查询该患者在指定时间范围内的所有处方明细数据。

## 业务流程

1. **第一步：通过处方号查询**
   - 使用传入的 `cfxh`（处方号）参数调用接口查询处方数据
   - 从查询结果中提取第一条数据的 `patient_id` 字段值

2. **第二步：使用患者ID重新查询**
   - 使用提取到的 `patient_id` 构建新的查询参数
   - 结合时间范围参数（`startTime` 和 `endTime`）查询该患者的所有处方明细数据

## 请求参数示例

```json
{
    "cardType": "5",
    "cfxh": "O250529003426",
    "startTime": "2025-06-20 00:00:00",
    "endTime": "2025-06-24 23:59:59",
    "fg_dps": "0",
    "send_flag": "1"
}
```

## 参数说明

- `cardType`: 必填，固定值为 "5"
- `cfxh`: 必填，处方号
- `startTime`: 可选，查询开始时间（格式：yyyy-MM-dd HH:mm:ss）
- `endTime`: 可选，查询结束时间（格式：yyyy-MM-dd HH:mm:ss）
- `fg_dps`: 可选，发药单标记（0：返药；1：退药），默认为 "0"
- `send_flag`: 可选，发送标识（0：未发送；1：已发送；2：已退药），默认为 "1"

## 响应结果

返回该患者在指定时间范围内的所有处方明细数据列表。

## 使用场景

- 当需要查询某个患者的历史处方记录时
- 当只知道一个处方号，但需要获取该患者的其他处方信息时
- 用于患者处方历史统计和分析

## 注意事项

1. 处方号必须存在且有效
2. 如果处方号查询不到数据，将抛出异常
3. 时间范围参数可选，不提供时将查询该患者的所有处方数据 