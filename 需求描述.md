2025-08-27 08:41:25.701 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.service.HangChuangService:89 - 开始查询门诊处方发药数据，查询参数：{"cfxh":"201312090620","patient_id":"","startTime":"2025-08-26 00:00:00","endTime":"2025-08-27 23:59:59","fg_dps":"0","send_flag":"1","cardType":"4"}
2025-08-27 08:41:25.701 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.service.HangChuangService:718 - 构建门诊发药明细查询参数完成，cardType=4，参数：{"start_time":"2025-08-26 00:00:00","send_flag":"1","end_time":"2025-08-27 23:59:59","type":"1","cfxh":"201312090620","fg_dps":"0"}
2025-08-27 08:41:25.702 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.HttpRequestUtil:60 - 查询门诊发药明细:请求地址：http://199.199.199.40:9999/api/TraceCode/outPatientDispenseDetail
2025-08-27 08:41:25.702 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.HttpRequestUtil:61 - 查询门诊发药明细:请求参数：{"start_time":"2025-08-26 00:00:00","send_flag":"1","end_time":"2025-08-27 23:59:59","type":"1","cfxh":"201312090620","fg_dps":"0"}
2025-08-27 08:41:25.884 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.HttpRequestUtil:73 - 查询门诊发药明细:响应状态码：200
2025-08-27 08:41:25.884 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.HttpRequestUtil:74 - 查询门诊发药明细:响应数据：
{
    "code": 0,
    "message": "查询成功",
    "dataList":
    [
        {
            "med_list_codg": "XA11CCA017E002010304036",
            "fixmedins_hilist_id": "77249-2933",
            "fixmedins_hilist_name": "阿法骨化醇软胶囊",
            "fixmedins_bchno": "29426360-77249-2933",
            "prsc_dr_certno": "341125199205313813",
            "prsc_dr_name": "黄帅",
            "phar_certno": "34112576042006",
            "phar_name": "陈云",
            "phar_prac_cert_no": "-",
            "mdtrt_sn": "34112025082797182341",
            "psn_name": "杨世宏",
            "manu_lotnum": "2411251",
            "expy_end": "2026-11-24",
            "rx_flag": "0",
            "trdn_flag": "1",
            "rxno": "22369917",
            "rx_circ_flag": "0",
            "rtal_docno": "*********",
            "stoout_no": "*********",
            "bchno": "",
            "sel_retn_cnt": 133.0,
            "min_sel_retn_cnt": 133.0,
            "sel_retn_unit": "粒",
            "his_dos_unit": "ug",
            "his_pac_unit": "粒",
            "sel_retn_time": "2025-08-27 08:40:32",
            "sel_retn_opter_name": "陈云",
            "mdtrt_setl_type": "1",
            "spec": "0.25ug*40粒",
            "prodentp_name": "青岛国信制药有限公司",
            "cfxh": "22369917",
            "cfmxxh": "29426360",
            "sjh": "*********",
            "patient_id": "582704",
            "his_con_ratio": "40",
            "send_flag": "1",
            "send_time": "2025-08-27 08:40:32",
            "dept_id": "1",
            "dept_name": "总院门诊药房",
            "window": "1"
        },
        {
            "med_list_codg": "XA11CCA017E002010304036",
            "fixmedins_hilist_id": "77249-2933",
            "fixmedins_hilist_name": "阿法骨化醇软胶囊",
            "fixmedins_bchno": "29426360-77249-2933",
            "prsc_dr_certno": "341125199205313813",
            "prsc_dr_name": "黄帅",
            "phar_certno": "34112576042006",
            "phar_name": "陈云",
            "phar_prac_cert_no": "-",
            "mdtrt_sn": "34112025082797182341",
            "psn_name": "杨世宏",
            "manu_lotnum": "2411251",
            "expy_end": "2026-11-24",
            "rx_flag": "0",
            "trdn_flag": "1",
            "rxno": "22369917",
            "rx_circ_flag": "0",
            "rtal_docno": "*********",
            "stoout_no": "*********",
            "bchno": "",
            "sel_retn_cnt": 227.0,
            "min_sel_retn_cnt": 227.0,
            "sel_retn_unit": "粒",
            "his_dos_unit": "ug",
            "his_pac_unit": "粒",
            "sel_retn_time": "2025-08-27 08:40:32",
            "sel_retn_opter_name": "陈云",
            "mdtrt_setl_type": "1",
            "spec": "0.25ug*40粒",
            "prodentp_name": "青岛国信制药有限公司",
            "cfxh": "22369917",
            "cfmxxh": "29426360",
            "sjh": "*********",
            "patient_id": "582704",
            "his_con_ratio": "40",
            "send_flag": "1",
            "send_time": "2025-08-27 08:40:32",
            "dept_id": "1",
            "dept_name": "总院门诊药房",
            "window": "1"
        }
    ]
}2025-08-27 08:41:25.885 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.service.HangChuangService:112 - his接口查询门诊发药明细成功，返回2条数据
2025-08-27 08:41:25.885 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.SaasHttpUtil:92 - queryTracDrugYz 请求地址：http://36.138.146.27:30800/saas-prod/prod/system/interface/pda/queryTracDrugYz
2025-08-27 08:41:25.886 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.SaasHttpUtil:95 - queryTracDrugYz 请求头：{"Authorization":"Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxMDAxMjcyLCJ1c2VyX2tleSI6IkgzNDExMjUwMDAwMzEwMDEyNzIyMDI1LTA4LTI1IiwidXNlcm5hbWUiOiJkeXp5eW16MDAzIn0.XiCtLBrEO11azsTu63tiOjyC7lIRtaGzGxcGUyTa41NcZv9pcajHmm89S3myNY6Mqcg3kEQi62qhfkK5xCImpQ","Access-Token":"Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxMDAxMjcyLCJ1c2VyX2tleSI6IkgzNDExMjUwMDAwMzEwMDEyNzIyMDI1LTA4LTI1IiwidXNlcm5hbWUiOiJkeXp5eW16MDAzIn0.XiCtLBrEO11azsTu63tiOjyC7lIRtaGzGxcGUyTa41NcZv9pcajHmm89S3myNY6Mqcg3kEQi62qhfkK5xCImpQ"}
2025-08-27 08:41:25.886 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.SaasHttpUtil:99 - queryTracDrugYz 请求数据：[
    {
        "drugCode": "77249-2933",
        "dispCnt": 133,
        "cfxh": "22369917",
        "cfmxxh": "29426360"
    },
    {
        "drugCode": "77249-2933",
        "dispCnt": 227,
        "cfxh": "22369917",
        "cfmxxh": "29426360"
    }
]2025-08-27 08:41:25.912 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.utils.SaasHttpUtil:114 - queryTracDrugYz 响应数据：{
    "msg": "操作成功",
    "code": 200,
    "data":
    [
        {
            "drugCode": "77249-2933",
            "isTrac": 0,
            "dispCnt": 133,
            "conRatio": 40.0000,
            "cfxh": "22369917",
            "cfmxxh": "29426360"
        },
        {
            "drugCode": "77249-2933",
            "isTrac": 0,
            "dispCnt": 227,
            "conRatio": 40.0000,
            "cfxh": "22369917",
            "cfmxxh": "29426360"
        }
    ]
}2025-08-27 08:41:25.913 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.service.HangChuangService:964 - 门诊药品追溯码库存信息处理完成，共处理2条数据，其中0条需要追溯
2025-08-27 08:41:25.923 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.service.impl.Nhsa3505ServiceImpl:90 - 开始异步保存3505销售记录，总数据量: 2
2025-08-27 08:41:25.932 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.service.impl.Nhsa3505ServiceImpl:189 - 3505销售记录保存完成，成功: 1, 失败: 0, 跳过: 1
2025-08-27 08:41:25.932 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.service.HangChuangService:130 - 查询门诊处方发药数据完成，最终返回记录数：2
2025-08-27 08:41:25.933 [582e9e8da99e4ce5] [tomcat-handler-8363] INFO  com.zsm.common.ConsoleLogAspect:69 - 
请求接口 : POST http://************:30000/api/dingYuanZongYuan/queryOutpatientPrescription
方法名称 : 查询门诊处方
接口方法 : com.zsm.controller.DingYuanZongYuanController.queryOutpatientPrescription
设备ip : *************
POST入参 ：{
    "cfxh": "201312090620",
    "patient_id": "",
    "startTime": "2025-08-26 00:00:00",
    "endTime": "2025-08-27 23:59:59",
    "fg_dps": "0",
    "send_flag": "1",
    "cardType": "4"
}出参 : {
    "code": 200,
    "msg": "操作成功",
    "data":
    [
        {
            "med_list_codg": "XA11CCA017E002010304036",
            "fixmedins_hilist_id": "77249-2933",
            "fixmedins_hilist_name": "阿法骨化醇软胶囊",
            "fixmedins_bchno": "29426360-77249-2933",
            "prsc_dr_name": "黄帅",
            "phar_name": "陈云",
            "phar_prac_cert_no": "-",
            "mdtrt_sn": "34112025082797182341",
            "psn_name": "杨世宏",
            "manu_lotnum": "2411251",
            "expy_end": "2026-11-24",
            "rx_flag": 0,
            "trdn_flag": 0,
            "rxno": "22369917",
            "rx_circ_flag": "0",
            "rtal_docno": "*********",
            "stoout_no": "*********",
            "bchno": "",
            "sel_retn_cnt": "133",
            "min_sel_retn_cnt": "133",
            "selRetnUnit": "粒",
            "hisDosUnit": "ug",
            "hisPacUnit": "粒",
            "sel_retn_time": "2025-08-27 08:40:32",
            "sel_retn_opter_name": "陈云",
            "mdtrt_setl_type": 1,
            "spec": "0.25ug*40粒",
            "prodentp_name": "青岛国信制药有限公司",
            "cfxh": "22369917",
            "cfmxxh": "29426360",
            "sjh": "*********",
            "patient_id": "582704",
            "his_con_ratio": "40.0000",
            "send_flag": 1,
            "send_time": "2025-08-27 08:40:32",
            "dept_id": "1",
            "dept_name": "总院门诊药房",
            "window": "1"
        },
        {
            "med_list_codg": "XA11CCA017E002010304036",
            "fixmedins_hilist_id": "77249-2933",
            "fixmedins_hilist_name": "阿法骨化醇软胶囊",
            "fixmedins_bchno": "29426360-77249-2933",
            "prsc_dr_name": "黄帅",
            "phar_name": "陈云",
            "phar_prac_cert_no": "-",
            "mdtrt_sn": "34112025082797182341",
            "psn_name": "杨世宏",
            "manu_lotnum": "2411251",
            "expy_end": "2026-11-24",
            "rx_flag": 0,
            "trdn_flag": 0,
            "rxno": "22369917",
            "rx_circ_flag": "0",
            "rtal_docno": "*********",
            "stoout_no": "*********",
            "bchno": "",
            "sel_retn_cnt": "227",
            "min_sel_retn_cnt": "227",
            "selRetnUnit": "粒",
            "hisDosUnit": "ug",
            "hisPacUnit": "粒",
            "sel_retn_time": "2025-08-27 08:40:32",
            "sel_retn_opter_name": "陈云",
            "mdtrt_setl_type": 1,
            "spec": "0.25ug*40粒",
            "prodentp_name": "青岛国信制药有限公司",
            "cfxh": "22369917",
            "cfmxxh": "29426360",
            "sjh": "*********",
            "patient_id": "582704",
            "his_con_ratio": "40.0000",
            "send_flag": 1,
            "send_time": "2025-08-27 08:40:32",
            "dept_id": "1",
            "dept_name": "总院门诊药房",
            "window": "1"
        }
    ]
}请求耗时: 281ms | 请求时间: 2025-08-27 08:41:25.651 | 响应时间: 2025-08-27 08:41:25.932

查询门诊发药明细接口返回的药品明显数据,可能存在不同批次的药品,导致一个药被拆成了两个明细显示
1. 需要合并相同`med_list_codg`药品id的药品,不同批次号的药品,合并后需要保留所有药品的明细数据,并相加`min_sel_retn_cnt`值为总和作为合并后的值
2. 重新计算`sel_retn_cnt`值,需要将所有明细的只进行相加，然后除以`his_con_ratio`,如果得出来的是正整数则保留,并赋值给`sel_retn_cnt`,否则只相加赋值`sel_retn_cnt`
