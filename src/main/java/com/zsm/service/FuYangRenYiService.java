package com.zsm.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.common.exception.BusinessException;
import com.zsm.entity.YsfStoTcTask;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.saas.request.GetTracCodgStoreDataRequest;
import com.zsm.model.saas.request.GetTracCodgStoreRequest;
import com.zsm.model.saas.request.QueryTracDrugRequest;
import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import com.zsm.model.saas.response.QueryTracDrugResponse;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.model.vo.SoapResponseVo;
import com.zsm.utils.SaasHttpUtil;
import com.zsm.utils.SoapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 阜阳人医业务处理服务类
 *
 * <AUTHOR>
 * @date 2025/6/2 下午10:23
 */
@Slf4j
@Service
public class FuYangRenYiService {

    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;
    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private Nhsa3506Service nhsa3506Service;

    /**
     * 查询门诊处方信息
     *
     * @param queryDto 查询参数
     * @return 门诊处方数据
     */
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(OutpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询门诊处方信息，参数: {}", queryDto);

            // 参数校验
            if (queryDto == null) {
                queryDto = new OutpatientPrescriptionQueryDto();
            }

            // 判断是否为退药查询
            boolean isReturnDrugQuery = "1".equals(queryDto.getFg_dps());
            log.info("查询类型: {}", isReturnDrugQuery ? "退药查询" : "发药查询");

            // 调用SOAP服务
            final SoapResponseVo mes0271 = SoapUtil.callSoapServiceWithParams("MES0271", queryDto);
            SoapUtil.checkBusinessCode(mes0271);
            OutpatientPrescriptionResponseVo prescriptionResponse = SoapUtil.parseOutpatientResponseData(mes0271);

            final List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList = prescriptionResponse.getDataList();
            log.info("门诊处方查询成功，返回{}条数据", dataList != null ? dataList.size() : 0);

            if (dataList != null) {
                // 只有发药查询才需要处理追溯码库存信息
                if (!isReturnDrugQuery) {
                    this.handleTracCodgStoreCydy(dataList);
                }

                // 只有发药查询才需要过滤已完成扫码任务的药品信息
                if (!isReturnDrugQuery) {
                    this.filterCompletedDispenseTasks(dataList);
                } else {
                    log.info("退药查询不进行发药任务状态过滤");
                }
            }

            // 异步保存到3505表中（发药和退药都需要保存）
            if (!isReturnDrugQuery) {
                nhsa3505Service.save3505Async(dataList);
            } else {
                nhsa3506Service.save3506Async(dataList);
            }

            return ApiResult.success(dataList);

        } catch (Exception e) {
            String errorMsg = "查询门诊处方异常: " + e.getMessage();
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }

    /**
     * 过滤已完成扫码任务的药品信息，防止后续业务重复上传造成脏数据
     * 仅适用于发药业务
     *
     * @param dataList 处方数据列表
     */
    private void filterCompletedDispenseTasks(List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList) {
        Iterator<OutpatientPrescriptionResponseVo.PrescriptionItem> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            OutpatientPrescriptionResponseVo.PrescriptionItem dispenseInfo = iterator.next();
            String outPresId = dispenseInfo.getCfxh();
            if (StringUtils.isEmpty(outPresId)) {
                continue;
            }

            // 1. 查询是否存在已完成且未删除的任务
            LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
            completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                    .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
            Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

            // 2. 如果存在已完成的任务，则过滤掉该处方数据
            if (completedTaskCount > 0) {
                iterator.remove();
                continue;
            }

            // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
            LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
            latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                    .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                    .orderByDesc(YsfStoTcTask::getIdTask)
                    .last("limit 1"); // 按创建时间降序

            YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

            // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段
            if (latestTask != null) {
                // 使用实体类中新增的字段
                dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ?
                        latestTask.getIdTask().toString() : null);
                dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ?
                        latestTask.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            }
        }
    }

    /**
     * 处理药品的追溯码库存信息。
     * <p>
     * 用于处理(zsmViewList)列表。
     * 1. 调用SaaS平台接口查询药品是否需要追溯以及HIS系统中的转换比。
     * 2. 更新药品对象的追溯标志和HIS转换比。
     * 3. 如果药品需要追溯，则调用SaaS平台接口获取药品的追溯码库存信息。
     * 4. 将获取到的库存信息填充回药品对象。
     *
     * @param zsmViewList 药品列表。
     */
    private void handleTracCodgStoreCydy(List<OutpatientPrescriptionResponseVo.PrescriptionItem> zsmViewList) {
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();

        // 1. 构建查询药品追溯信息的请求列表
        List<QueryTracDrugRequest> queryTracDrugRequestList = zsmViewList.stream().map(i ->
                QueryTracDrugRequest.builder()
                        .cfxh(i.getCfxh())
                        .cfmxxh(i.getCfmxxh())
                        .drugCode(i.getFixmedins_hilist_id())
                        .dispCnt(Integer.valueOf(i.getMin_sel_retn_cnt()))
                        .build()).collect(Collectors.toList());
        // 调用SaaS接口批量查询药品追溯信息
        Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugMap(userInfo.getAuthorization(), queryTracDrugRequestList);


        List<GetTracCodgStoreDataRequest> dataList = new ArrayList<>(); // 用于存放需要查询追溯码库存的药品请求
        for (OutpatientPrescriptionResponseVo.PrescriptionItem i : zsmViewList) {
            if (queryTracDrugMap.containsKey(i.getCfmxxh())) {
                QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(i.getCfmxxh());
                i.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac())); // 设置是否追溯标志
                i.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio())); // 设置HIS转换比例
                // 如果存在HIS转换比例，则计算实际可选数量
                // if (!ObjectUtils.isEmpty(i.getHis_con_ratio())) {
                //     BigDecimal divide = new BigDecimal(i.getMin_sel_retn_cnt()).divide(new BigDecimal(i.getHis_con_ratio()), 2, RoundingMode.HALF_UP);
                //     i.setSel_retn_cnt(divide.toString());
                // }
            }
            // 如果药品需要追溯
            if (i.getTrdn_flag() == 1) {
                GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
                        .cfxh(i.getCfxh())
                        .cfmxxh(i.getCfmxxh())
                        .dispCnt(Integer.valueOf(i.getMin_sel_retn_cnt()))
                        .drugCode(i.getFixmedins_hilist_id())
                        .build();
                dataList.add(build);
            }
        }
        Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
        // 如果有需要查询库存的药品
        if (!ObjectUtils.isEmpty(dataList)) {
            // 调用SaaS接口批量获取药品追溯码库存信息
            List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(userInfo.getAuthorization(), GetTracCodgStoreRequest.builder()
                    .dataList(dataList).build());
            // 将库存信息列表转换为Map
            tracCodgStoreMap = tracCodgStore.stream()
                    .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
        }

        // 4. 将获取到的库存信息填充回药品对象
        for (OutpatientPrescriptionResponseVo.PrescriptionItem i : zsmViewList) {
            if (i.getTrdn_flag() == 1) { // 只处理需要追溯的药品
                if (tracCodgStoreMap.containsKey(i.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(i.getCfmxxh());
                    i.setDrugCode(tracCodgStoreData.getDrugCode());
                    i.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    // 设置完整的库存对象
                    i.setTracCodgStore(tracCodgStoreData);
                    i.setDispCnt(tracCodgStoreData.getDispCnt());
                    i.setCurrNum(tracCodgStoreData.getCurrNum());
                }
            }
        }

    }

    /**
     * 查询住院处方信息
     *
     * @param queryDto 查询参数
     * @return 住院处方数据
     */
    public ApiResult<List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryInpatientPrescription(InpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询住院处方信息，参数: {}", queryDto);

            // 参数校验
            if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }

            // 调用SOAP服务

            final SoapResponseVo mes0272 = SoapUtil.callSoapServiceWithParams("MES0272", queryDto);
            final JSONObject jsonStr =mes0272.getJsonResult();

            Integer businessCode = jsonStr.getInt("code", -1);
            if (businessCode != 0) {
                String businessMessage = jsonStr.getStr("message", "业务执行失败");
                log.error("SOAP业务执行失败，业务状态码: {}, 错误信息: {}", businessCode, businessMessage);
                return ApiResult.error(businessMessage);
            }

            InpatientPrescriptionResponseVo prescriptionResponse = SoapUtil.parseInpatientResponseData(jsonStr);

            log.info("住院处方查询成功，返回{}条数据",
                    prescriptionResponse.getDataList() != null ? prescriptionResponse.getDataList()
                            .size() : 0);

            return ApiResult.success(prescriptionResponse.getDataList());

        } catch (Exception e) {
            String errorMsg = "查询住院处方异常: " + e.getMessage();
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }

}
