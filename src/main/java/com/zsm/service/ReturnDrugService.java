package com.zsm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.common.exception.BusinessException;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfStoDps;
import com.zsm.entity.YsfStoDpsSub;
import com.zsm.entity.YsfStoTc;
import com.zsm.entity.YsfStoTcStatus;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.TraceabilityUploadDto;
import com.zsm.model.enums.DispenseOrderStatusEnum;
import com.zsm.model.enums.SdTcStatusEnum;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.model.vo.ReturnDrugResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退药服务类
 *
 * <AUTHOR>
 * @date 2025/6/9 20:33
 */
@Slf4j
@Service
public class ReturnDrugService {
    @Resource
    private YsfStoDpsService ysfStoDpsService;
    @Resource
    private YsfStoDpsSubService ysfStoDpsSubService;
    @Resource
    private YsfStoTcService ysfStoTcService;
    @Resource
    private YsfStoTcStatusService ysfStoTcStatusService;
    @Resource
    private Nhsa3506Service nhsa3506Service;
    @Resource
    private Nhsa3505Service nhsa3505Service;

    /**
     * 返回药物
     *
     * @param request 请求
     * @return {@link ApiResult }<{@link ReturnDrugResultVo }>
     */
    public ApiResult<ReturnDrugResultVo> returnDrug(TraceabilityUploadDto request) {
        // 获取token登录的账号信息
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
        String userId = userInfo.getUser().getUserId().toString(); // 系统用户ID
        String userName = userInfo.getUser().getUserName(); // 系统用户名

        // 初始化结果收集器
        List<String> successIds = new ArrayList<>();
        List<String> failIds = new ArrayList<>();
        Map<String, String> failMessages = new HashMap<>();
        Map<String, String> successDetails = new HashMap<>();

        // 校验请求参数
        if (request == null || request.getPrescriptions() == null || request.getPrescriptions().isEmpty()) {
            return ApiResult.error("请求参数不能为空");
        }

        try {
            // 执行数据保存操作
            for (TraceabilityUploadDto.PrescriptionItem prescription : request.getPrescriptions()) {
                String outPresId = prescription.getOutPresId();
                
                if (StringUtils.isEmpty(outPresId)) {
                    String errorMsg = "处方ID不能为空";
                    failIds.add("unknown");
                    failMessages.put("unknown", errorMsg);
                    log.error(errorMsg);
                    continue;
                }
                
                try {
                    // 第一步：处理本地业务数据
                    processPrescription(prescription, userName, request);
                    
                    // 第二步：调用医保接口
                    nhsa3506Service.returnDrugAndSave3506Info(prescription, userId, userName);
                    
                    // 成功处理
                    successIds.add(outPresId);
                    successDetails.put(outPresId, "退药处理成功，已同步医保平台");
                    log.info("处方[{}]退药处理成功", outPresId);
                    
                } catch (Exception e) {
                    // 处理失败
                    String errorMsg = "处理失败: " + e.getMessage();
                    failIds.add(outPresId);
                    failMessages.put(outPresId, errorMsg);
                    log.error("处方[{}]退药处理失败: {}", outPresId, e.getMessage(), e);
                    
                    // 如果是关键业务异常且配置为严格模式，则抛出异常触发回滚
                    if (e instanceof BusinessException) {
                        throw e;
                    }
                }
            }

            // 构造返回结果
            ReturnDrugResultVo result = buildReturnResult(successIds, failIds, failMessages, successDetails);
            
            // 根据处理结果确定返回状态
            if (failIds.isEmpty()) {
                return ApiResult.success(result);
            } else if (successIds.isEmpty()) {
                ApiResult<ReturnDrugResultVo> errorResult = ApiResult.error("所有处方退药失败");
                errorResult.setData(result);
                return errorResult;
            } else {
                ApiResult<ReturnDrugResultVo> partialResult = ApiResult.error("部分处方退药失败");
                partialResult.setData(result);
                return partialResult;
            }

        } catch (Exception e) {
            log.error("退药失败: {}", e.getMessage(), e);
            throw new BusinessException("退药失败: " + e.getMessage());
        }
    }

    /**
     * 确保3505数据已同步到医保平台
     *
     * @param cfmxxh 处方明细序号
     * @param cfxh   处方序号
     */
    private void ensure3505Synced(String cfmxxh, String cfxh) {
        // 查询本地3505状态
        boolean uploaded = nhsa3505Service.lambdaQuery()
                .eq(Nhsa3505::getCfmxxh, cfmxxh)
                .eq(Nhsa3505::getCfxh, cfxh)
                .eq(Nhsa3505::getHsaSyncStatus, "1")
                .exists();
        
        if (!uploaded) {
            log.info("3505数据未同步，开始补传，cfmxxh: {}, cfxh: {}", cfmxxh, cfxh);
            ApiResult<String> result = nhsa3505Service.manualUploadDataToPlatform(cfmxxh, cfxh);
            if (!result.isSuccess()) {
                throw new BusinessException("3505补传失败，原因：" + result.getMsg());
            }
            log.info("3505数据补传成功，cfmxxh: {}, cfxh: {}", cfmxxh, cfxh);
        }
    }

    /**
     * 处理单个处方的追溯码上传
     *
     * @param prescription 处方信息
     * @param userName     用户名
     */
    private void processPrescription(TraceabilityUploadDto.PrescriptionItem prescription,
                                     String userName, TraceabilityUploadDto request) {
        // 1. 获取或创建发药单
        YsfStoDps stoDps = getOrCreateStoDps(prescription.getOutPresId(), prescription, userName, request);

        // 4. 处理每个药品明细的追溯码
        for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
            // 确保3505数据已同步
            ensure3505Synced(drugItem.getCfmxxh(), drugItem.getCfxh());
            
            processdrugItem(drugItem, stoDps, userName, prescription.getIdDept());
        }

    }

    /**
     * 获取或创建发药单
     */
    private YsfStoDps getOrCreateStoDps(String outPresId, TraceabilityUploadDto.PrescriptionItem prescription,
                                        String userName, TraceabilityUploadDto request) {
        // 查询是否存在发药单
        YsfStoDps stoDps = ysfStoDpsService.lambdaQuery()
                .eq(YsfStoDps::getCfxh, outPresId)
                .eq(YsfStoDps::getDelFlag, "0")
                .last("limit 1")
                .one();

        // TODO: 这块的业务逻辑有问题,退药会导致后面的业务空异常,发药单号需要做下处理,退药时需要跟发药时错差异,不然会匹配到发药数据
        if (stoDps == null) {
            // 创建新的发药单
            stoDps = new YsfStoDps();
            stoDps.setCfxh(outPresId);
            stoDps.setSdDps(request.getFgDps()); // 处方单
            stoDps.setPatientId(prescription.getPatId());
            stoDps.setPsnName(prescription.getPatName());
            stoDps.setCardNo(prescription.getCardNo());
            stoDps.setFgStatus(DispenseOrderStatusEnum.FULL_RETURN.getCode());
            stoDps.setFgDps("1"); // 发药单
            stoDps.setFgPrint("0"); // 未打印


            // 设置机构信息
            stoDps.setIdOrg(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            stoDps.setOrgId(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            stoDps.setOrgName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

            // 设置创建和修改信息
            stoDps.setCreateBy(userName);
            stoDps.setCreateTime(LocalDateTime.now());
            stoDps.setUpdateBy(userName);
            stoDps.setUpdateTime(LocalDateTime.now());
            stoDps.setDelFlag("0");

            // 设置发药窗口和工作人员信息
            stoDps.setIdDept(prescription.getIdDept());
            stoDps.setWindow(request.getWindow());
            stoDps.setSelRetnOpterId(request.getSelRetnOpterId());

            // 插入数据库
            ysfStoDpsService.save(stoDps);
        }

        return stoDps;
    }

    /**
     * 处理药品明细项
     */
    private void processdrugItem(TraceabilityUploadDto.DrugItem drugItem, YsfStoDps stoDps, String userName, String idDept) {
        String outPresdetailid = drugItem.getOutPresdetailid();
        String drugtracinfo = drugItem.getDrugtracinfoScanned();

        // 拆分追溯码
        String[] drugtracinfoArray = drugtracinfo.split(",");
        int drugtracinfoArrayLength = drugtracinfoArray.length;

        if (StringUtils.isEmpty(outPresdetailid)) {
            throw new BusinessException("处方明细ID不能为空");
        }


        // 2. 获取或创建发药单明细
        YsfStoDpsSub stoDpsSub = getOrCreateStoDpsSub(stoDps, drugItem, userName,drugtracinfo, drugtracinfoArrayLength);

        // 3. 处理追溯码（商品追溯码主表）一个追溯码一个记录
        for (String drugZsm : drugtracinfoArray) {

            if (StringUtils.isEmpty(drugZsm.trim())) {
                continue;
            }

            processTraceabilityCode(drugZsm, drugItem, stoDpsSub.getId().toString(), userName, idDept);
        }


    }

    /**
     * 获取或创建发药单明细
     */
    private YsfStoDpsSub getOrCreateStoDpsSub(YsfStoDps stoDps, TraceabilityUploadDto.DrugItem drugItem, String userName,String drugtracinfo, int drugtracinfoArrayLength) {
        // 查询是否存在发药单明细
        YsfStoDpsSub stoDpsSub = ysfStoDpsSubService.lambdaQuery()
                .eq(YsfStoDpsSub::getIdDps, stoDps.getIdDps())
                .eq(YsfStoDpsSub::getCfmxxh, drugItem.getOutPresdetailid())
                .eq(YsfStoDpsSub::getDelFlag, "0")
                .last("limit 1")
                .one();

        if (stoDpsSub == null) {
            // 创建新的发药单明细
            stoDpsSub = new YsfStoDpsSub();
            stoDpsSub.setIdDps(stoDps.getIdDps().toString());
            stoDpsSub.setCfmxxh(drugItem.getOutPresdetailid());
            stoDpsSub.setCfxh(stoDps.getCfxh());
            // 原始his的cfxh和cfmxxh
            stoDpsSub.setOriId(drugItem.getOriId());
            stoDpsSub.setOriCfmxxh(drugItem.getOriCfmxxh());

            stoDpsSub.setDrugCode(drugItem.getDrugCode());
            stoDpsSub.setNaFee(drugItem.getDrugName());
            stoDpsSub.setPriceSale(drugItem.getPrice() != null ? BigDecimal.valueOf(drugItem.getPrice()) : null);
            stoDpsSub.setSelRetnCnt(drugItem.getQuantity());
            stoDpsSub.setAmtTotal(drugItem.getAmount() != null ? BigDecimal.valueOf(drugItem.getAmount()) : null);
            stoDpsSub.setAmtTotalDps(drugItem.getAmount() != null ? BigDecimal.valueOf(drugItem.getAmount()) : null);
            stoDpsSub.setUnitSale(drugItem.getUnit());
            stoDpsSub.setUnitSaleFactor(drugItem.getMinDoseCount());
            stoDpsSub.setQuantity(drugItem.getQuantity());
            stoDpsSub.setUnit(drugItem.getUnit());

            // 设置机构信息
            stoDpsSub.setIdOrg(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            stoDpsSub.setOrgId(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            stoDpsSub.setOrgName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

            // 设置创建和修改信息
            stoDpsSub.setCreateBy(userName);
            stoDpsSub.setCreateTime(LocalDateTime.now());
            stoDpsSub.setUpdateBy(userName);
            stoDpsSub.setUpdateTime(LocalDateTime.now());
            stoDpsSub.setDelFlag("0");

            stoDpsSub.setDrugtracinfo(drugtracinfo);
            stoDpsSub.setTracCnt(drugtracinfoArrayLength);

            // 插入数据库
            ysfStoDpsSubService.save(stoDpsSub);
        }
        return stoDpsSub;
    }

    /**
     * 处理追溯码
     *
     * @param drugtracinfo 追溯码
     * @param drugItem     药品项目
     * @param stoDpsSubId  发药单明细id
     * @param userName     用户名
     * @param idDept       部门id
     */
    private void processTraceabilityCode(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem, String stoDpsSubId, String userName, String idDept) {
        // 查询是否存在追溯码记录
        YsfStoTc stoTc = ysfStoTcService.lambdaQuery()
                .eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0").last("limit 1")
                .one();

        if (stoTc != null) {
            // 更新追溯码记录，将剩余数量设为退药的数量,拆零的为退药数量,整盒的为包装系数
            if (drugItem.getTrdnFlag() == 0) {
                stoTc.setAmountRem(BigDecimal.ONE);
            }
            stoTc.setUpdateBy(userName);
            stoTc.setUpdateTime(LocalDateTime.now());
            ysfStoTcService.updateById(stoTc);
        } else {
            // 创建新的追溯码记录
            stoTc = new YsfStoTc();
            stoTc.setDrugtracinfo(drugtracinfo);
            stoTc.setDrugCode(drugItem.getDrugCode());

            if (drugItem.getTrdnFlag() == 0) {
                stoTc.setAmountRem(BigDecimal.ONE);
            }
            stoTc.setUnitSaleFactor(drugItem.getMinDoseCount());
            stoTc.setUnitTc(drugItem.getMinPackingName());
            stoTc.setFgActive("1"); // 有效
            stoTc.setIdDept(idDept);

            // 设置机构信息
            stoTc.setIdOrg(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            stoTc.setOrgId(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            stoTc.setOrgName(NhsaAccountConstant.getNhsaAccount().getMedicalName());
            stoTc.setSdTcManage("简易管理");

            // 设置创建和修改信息
            stoTc.setCreateBy(userName);
            stoTc.setCreateTime(LocalDateTime.now());
            stoTc.setUpdateBy(userName);
            stoTc.setUpdateTime(LocalDateTime.now());
            stoTc.setDelFlag("0");

            // 插入数据库
            ysfStoTcService.save(stoTc);
        }

        // 创建追溯码状态记录
        createTraceabilityStatus(drugtracinfo, drugItem, stoDpsSubId, stoTc.getIdTc(), userName, idDept);
    }

    /**
     * 创建追溯码状态记录
     *
     * @param drugtracinfo 追溯码
     * @param drugItem     药品项目
     * @param stoDpsSubId  发药单明细id
     * @param idTc         追溯码id
     * @param userName     用户名
     * @param idDept       部门id
     */
    private void createTraceabilityStatus(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem, String stoDpsSubId, Long idTc, String userName, String idDept) {
        YsfStoTcStatus tcStatus = new YsfStoTcStatus();
        // TODO:目前入参没有区分住院还是门诊退药,暂时只退门诊退药业务
        tcStatus.setSdTcStatus(SdTcStatusEnum.OUTPATIENT_RETURN.getCode()); // 门诊发药
        tcStatus.setSdTcManage("1"); // 简易管理模式
        tcStatus.setIdBizOri(stoDpsSubId);
        tcStatus.setCfmxxh(drugItem.getOutPresdetailid());
        tcStatus.setDrugCode(drugItem.getDrugCode());
        tcStatus.setDrugtracinfo(drugtracinfo);
        tcStatus.setSdTc("2"); // 商品追溯码
        if (drugItem.getTrdnFlag() == 0) {
            tcStatus.setSelRetnCnt(drugItem.getQuantity());
        } else {
            tcStatus.setSelRetnCnt(drugItem.getMinDoseCount());
        }
        tcStatus.setFgPack(drugItem.getTrdnFlag().toString());
        tcStatus.setFgUp("0"); // 未上传
        tcStatus.setFgActive("1"); // 有效
        tcStatus.setIdDept(idDept);
        tcStatus.setIdTc(idTc);

        // 设置机构信息
        tcStatus.setIdOrg(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
        tcStatus.setOrgId(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
        tcStatus.setOrgName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

        // 设置创建和修改信息
        tcStatus.setCreateBy(userName);
        tcStatus.setCreateTime(LocalDateTime.now());
        tcStatus.setUpdateBy(userName);
        tcStatus.setUpdateTime(LocalDateTime.now());
        tcStatus.setDelFlag("0");

        // 插入数据库
        ysfStoTcStatusService.save(tcStatus);
    }

    /**
     * 构建退药结果对象
     *
     * @param successIds     成功处理的处方ID列表
     * @param failIds        失败处理的处方ID列表
     * @param failMessages   失败信息映射
     * @param successDetails 成功详情映射
     * @return 退药结果对象
     */
    private ReturnDrugResultVo buildReturnResult(List<String> successIds, List<String> failIds, 
                                                Map<String, String> failMessages, Map<String, String> successDetails) {
        ReturnDrugResultVo result = new ReturnDrugResultVo();
        result.setSuccess(successIds);
        result.setFail(failIds);
        result.setFailMessages(failMessages);
        result.setSuccessDetails(successDetails);
        
        // 构建统计信息
        ReturnDrugResultVo.StatisticsInfo statistics = new ReturnDrugResultVo.StatisticsInfo();
        statistics.setTotalCount(successIds.size() + failIds.size());
        statistics.setSuccessCount(successIds.size());
        statistics.setFailCount(failIds.size());
        
        // 确定总体状态
        if (failIds.isEmpty()) {
            statistics.setOverallStatus("SUCCESS");
        } else if (successIds.isEmpty()) {
            statistics.setOverallStatus("FAIL");
        } else {
            statistics.setOverallStatus("PARTIAL");
        }
        
        result.setStatistics(statistics);
        return result;
    }
}
