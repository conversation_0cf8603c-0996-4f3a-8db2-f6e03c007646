package com.zsm.service;


import com.zsm.model.ApiResult;
import com.zsm.constant.SyncAccountEnum;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.model.vo.ProcessingResultVo;
import com.zsm.model.vo.DispensingDetailVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发药服务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface DispensingService {

    /**
     * 根据业务子ID查询发药明细
     *
     * @param bizSubId 业务子ID，通常是发药单明细ID或处方明细ID
     * @return 发药明细信息
     */
    DispensingDetailVo getDetailByBizSubId(String bizSubId);

    /**
     * 住院药房拆零确认定时任务处理方法
     * 自动获取住院领药数据，进行拆零确认处理
     */
    void processInpatientDispenseConfirmation();

    /**
     * 按指定时间范围处理住院拆零确认数据
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    ApiResult<ProcessingResultVo> processInpatientDispenseConfirmationByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 处理指定时间段的数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 包含处理结果详情的API响应对象
     */
    ApiResult<ProcessingResultVo> processTimeSlot(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 处理单个批次的数据
     *
     * @param account   同步账号枚举
     * @param token     访问令牌
     * @param batch     批次数据
     * @param ymfUserId 用户ID
     * @return 实际确认处理的数量
     */
    int processSingleBatch(SyncAccountEnum account, String token, List<InPatientDispenseDetailBindScatteredVo> batch, Long ymfUserId);
}