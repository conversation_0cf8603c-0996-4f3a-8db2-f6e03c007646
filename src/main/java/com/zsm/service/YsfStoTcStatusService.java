package com.zsm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsm.entity.YsfStoTcStatus;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.YsfStoTcStatusQueryDto;

/**
 * <p>
 * 追溯码状态记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
public interface YsfStoTcStatusService extends IService<YsfStoTcStatus> {
/**
     * 查询追溯码流转记录列表
     *
     * @param queryDto 查询条件
     * @return 分页结果
     */
    TableDataInfo queryStatusRecordList(YsfStoTcStatusQueryDto queryDto);
}
