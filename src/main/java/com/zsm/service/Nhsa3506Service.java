package com.zsm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsm.entity.Nhsa3506;
import com.zsm.model.dto.TraceabilityUploadDto;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;

import java.util.List;

/**
 * <p>
 * 3506结算记录报文表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface Nhsa3506Service extends IService<Nhsa3506> {

    /**
     * 异步保存3506退药记录
     * @param dataList 门诊处方数据列表
     */
    void save3506Async(List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList);

    /**
     * 上传3506报文,退药业务
     *
     * @param prescription 处方
     * @param userId       用户id
     * @param userName     用户名
     */
    void returnDrugAndSave3506Info(TraceabilityUploadDto.PrescriptionItem prescription, String userId, String userName);
}
