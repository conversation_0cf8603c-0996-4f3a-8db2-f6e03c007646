package com.zsm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.Nhsa3506;
import com.zsm.mapper.Nhsa3506Mapper;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.dto.TraceabilityUploadDto;
import com.zsm.model.nhsa.request.DrugTracInfo;
import com.zsm.model.nhsa.request.fsi3506.Selinfo3506;

import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.service.Nhsa3506Service;
import com.zsm.service.SignNoCacheService;
import com.zsm.utils.DateUtils;
import com.zsm.utils.NhsaHttpUtil;
import com.zsm.utils.NhsaRetryUtil;


import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 3506结算记录报文表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Slf4j
@Service
public class Nhsa3506ServiceImpl extends ServiceImpl<Nhsa3506Mapper, Nhsa3506> implements Nhsa3506Service {

    @Resource
    private SignNoCacheService signNoCacheService;
    @Resource
    private NhsaRetryUtil nhsaRetryUtil;

    @Async
    @Override
    public void save3506Async(List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("门诊处方数据列表为空，跳过保存操作");
            return;
        }

        log.info("开始异步保存3506退药记录，总数据量: {}", dataList.size());

        try {
            List<Nhsa3506> saveList = new ArrayList<>();
            int skipCount = 0;

            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : dataList) {
                // 检查必要字段
                String[] arr = {"X", "Z"};
                boolean medListCodgValid = false;
                if (StringUtils.hasText(item.getMed_list_codg())) {
                    for (String prefix : arr) {
                        if (item.getMed_list_codg().startsWith(prefix)) {
                            medListCodgValid = true;
                            break;
                        }
                    }
                }
                if (!StringUtils.hasText(item.getFixmedins_bchno()) || !medListCodgValid) {
                    log.warn("定点医疗机构批次号为空或药品编码不符合要求，跳过该条记录: {}", item);
                    skipCount++;
                    continue;
                }

                // 通过fixmedins_bchno查询是否已存在记录
                LambdaQueryWrapper<Nhsa3506> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Nhsa3506::getFixmedinsBchno, item.getFixmedins_bchno());

                if (count(queryWrapper) > 0) {
                    log.info("记录已存在，跳过保存 fixmedins_bchno: {}", item.getFixmedins_bchno());
                    skipCount++;
                    continue;
                }

                // 转换为Nhsa3506实体
                Nhsa3506 entity = convertPrescriptionItemToNhsa3506(item);
                if (entity != null) {
                    saveList.add(entity);
                }
            }

            // 批量保存
            if (!saveList.isEmpty()) {
                boolean result = saveBatch(saveList);
                if (result) {
                    log.info("3506退药记录批量保存成功，保存数量: {}, 跳过数量: {}", saveList.size(), skipCount);
                } else {
                    log.error("3506退药记录批量保存失败");
                }
            } else {
                log.info("没有新的记录需要保存，跳过数量: {}", skipCount);
            }

        } catch (Exception e) {
            log.error("异步保存3506退药记录发生异常", e);
        }
    }

    /**
     * 将门诊处方药品转换为Nhsa3506实体
     */
    private Nhsa3506 convertPrescriptionItemToNhsa3506(OutpatientPrescriptionResponseVo.PrescriptionItem item) {
        try {
            Nhsa3506 entity = new Nhsa3506();

            entity.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            entity.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

            // 基本药品信息
            entity.setMedListCodg(item.getMed_list_codg());
            entity.setFixmedinsHilistId(item.getFixmedins_hilist_id());
            entity.setFixmedinsHilistName(item.getFixmedins_hilist_name());
            entity.setFixmedinsBchno(item.getFixmedins_bchno());

            // 患者信息
            entity.setPsnName(item.getPsn_name());
            entity.setMdtrtSn(item.getMdtrt_sn());

            entity.setCfxh(item.getCfxh());
            entity.setCfmxxh(item.getCfmxxh());

            // 生产信息
            if (StringUtils.hasText(item.getManu_lotnum())) {
                entity.setManuLotnum(item.getManu_lotnum());
            } else {
                entity.setManuLotnum("-");
            }

            if (StringUtils.hasText(item.getManu_date())) {
                entity.setManuDate(DateUtils.parseDate(item.getManu_date()));
            }
            if (StringUtils.hasText(item.getExpy_end())) {
                entity.setExpyEnd(DateUtils.parseDate(item.getExpy_end()));
            }

            // 标志信息
            entity.setRxFlag(item.getRx_flag() != null ? String.valueOf(item.getRx_flag()) : null);
            entity.setTrdnFlag(item.getTrdn_flag() != null ? String.valueOf(item.getTrdn_flag()) : null);

            // 退药信息,数量和时间
            if (StringUtils.hasText(item.getSel_retn_cnt())) {
                entity.setSelRetnCnt(new BigDecimal(item.getSel_retn_cnt()));
            }
            if (StringUtils.hasText(item.getSel_retn_time())) {
                entity.setSelRetnTime(DateUtils.parseDateTime(item.getSel_retn_time()));
            }
            entity.setSelRetnOpterName(item.getSel_retn_opter_name());

            // 生产企业信息
            entity.setHisEntpName(item.getProdentp_name());

            // 备注信息存储规格
            if (StringUtils.hasText(item.getSpec())) {
                entity.setMemo(item.getSpec());
            }

            // 系统信息
            LocalDateTime now = LocalDateTime.now();
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setDeleteFlag("0");
            entity.setHsaSyncStatus("0"); // 未同步状态

            // 设置必填字段的默认值（如果为空）
            if (!StringUtils.hasText(entity.getFixmedinsHilistName())) {
                entity.setFixmedinsHilistName("-");
            }
            if (!StringUtils.hasText(entity.getPsnName())) {
                entity.setPsnName("-");
            }
            if (!StringUtils.hasText(entity.getSelRetnOpterName())) {
                entity.setSelRetnOpterName("-");
            }
            if (!StringUtils.hasText(entity.getHisEntpName())) {
                entity.setHisEntpName("-");
            }

            // 根据token获取用户信息,设置所属的用户信息
            try {
                SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
                if (userInfo != null && userInfo.getUser() != null) {
                    entity.setCreateBy(userInfo.getUser().getUserName());
                    entity.setUpdateBy(userInfo.getUser().getUserName());
                    entity.setYmfUserName(userInfo.getUser().getUserName());
                    entity.setYmfNickName(userInfo.getUser().getNickName());
                    entity.setYmfUserId(userInfo.getUser().getUserId());
                }
            } catch (Exception e) {
                log.info("获取用户信息失败，使用默认值", e);
            }

            return entity;

        } catch (Exception e) {
            log.error("转换PrescriptionItem到Nhsa3506实体时发生异常: {}", item, e);
            return null;
        }
    }


    @Override
    public void returnDrugAndSave3506Info(TraceabilityUploadDto.PrescriptionItem prescription, String userId, String userName) {
        try {
            log.info("开始处理退药数据并保存3506信息，处方ID: {}, 用户: {}", prescription.getOutPresId(), userName);
            List<Nhsa3506> returnList = new ArrayList<>();
            // 第一步：根据处方明细查找对应的3505数据并转换为3506数据
            for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
                String outPresdetailid = drugItem.getOutPresdetailid();
                String drugtracinfo = drugItem.getDrugtracinfoScanned();

                if (!StringUtils.hasText(outPresdetailid)) {
                    log.warn("处方明细ID为空，跳过该条记录");
                    continue;
                }

                if (!StringUtils.hasText(drugtracinfo)) {
                    log.warn("追溯码信息为空，跳过该条记录，outPresdetailid: {}", outPresdetailid);
                    continue;
                }

                // 检查是否已存在相同的3506记录
                final Nhsa3506 record = this.lambdaQuery().eq(Nhsa3506::getCfmxxh, drugItem.getCfmxxh()).last("limit 1").one();

                record.setDrugtracinfo(drugtracinfo);
                returnList.add(record);
            }

            // 第二步：查询未同步的3506数据并上传到两定接口
            uploadReturnDataToPlatform(returnList);

            log.info("完成退药数据处理和3506信息保存，处方ID: {}", prescription.getOutPresId());

        } catch (Exception e) {
            log.error("处理退药数据并保存3506信息时发生异常，处方ID: {}", prescription.getOutPresId(), e);
            throw new RuntimeException("处理退药数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传退药数据到两定接口平台
     *
     * @param returnList 退药列表
     */
    private void uploadReturnDataToPlatform(List<Nhsa3506> returnList) {
        try {
            log.info("查询到需要上传的退药数据数量: {}", returnList.size());

            // 获取医保账户信息
            NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

            // 获取今天的日期和默认日期
            LocalDate today = LocalDate.now();
            LocalDate lastYearToday = today.minusYears(1);
            LocalDate nextYearToday = today.plusYears(1);

            // 统计信息
            int successCount = 0;
            int failCount = 0;

            // 逐条处理数据
            for (Nhsa3506 nhsa3506 : returnList) {
                try {
                    // 构建3506请求对象
                    Selinfo3506 selinfo3506 = buildSelinfo3506(nhsa3506, lastYearToday, nextYearToday);

                    // 调用3506接口上传（带自动重试）                    
                    NhsaCityResponse response = nhsaRetryUtil.executeWithRetry(nhsaAccount, 
                        currentSignNo -> NhsaHttpUtil.fsi3506(currentSignNo, selinfo3506, nhsaAccount));

                    // 处理响应结果
                    if (response.getBody().getInfcode() == 0) {
                        // 上传成功
                        String nowTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        nhsa3506.setHsaSyncRemark("上传两定接口成功 " + nowTime);
                        nhsa3506.setHsaSyncStatus("1");
                        nhsa3506.setHsaSyncTime(LocalDateTime.now());
                        nhsa3506.setUpdateTime(LocalDateTime.now());
                        updateById(nhsa3506);

                        successCount++;
                        log.info("成功上传退药数据，ID: {}, fixmedinsBchno: {}", nhsa3506.getId(), nhsa3506.getFixmedinsBchno());
                    } else {
                        // 上传失败
                        String errorMsg = "上传两定接口失败，错误内容：" + response.getBody().getErr_msg();
                        nhsa3506.setHsaSyncRemark(errorMsg);
                        nhsa3506.setHsaSyncStatus("2");
                        nhsa3506.setHsaSyncTime(LocalDateTime.now());
                        nhsa3506.setUpdateTime(LocalDateTime.now());
                        updateById(nhsa3506);

                        failCount++;
                        log.error("上传退药数据失败，ID: {}, fixmedinsBchno: {}, 错误: {}",
                                nhsa3506.getId(), nhsa3506.getFixmedinsBchno(), response.getBody().getErr_msg());
                    }
                } catch (Exception e) {
                    failCount++;
                    String errorMsg = "处理退药数据时发生异常: " + e.getMessage();
                    nhsa3506.setHsaSyncRemark(errorMsg);
                    nhsa3506.setHsaSyncStatus("2");
                    nhsa3506.setHsaSyncTime(LocalDateTime.now());
                    nhsa3506.setUpdateTime(LocalDateTime.now());
                    updateById(nhsa3506);

                    log.error("处理退药数据时发生异常，ID: {}, fixmedinsBchno: {}",
                            nhsa3506.getId(), nhsa3506.getFixmedinsBchno(), e);
                }
            }

            log.info("退药数据上传完成，总数: {}, 成功: {}, 失败: {}", returnList.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("上传退药数据到两定接口平台时发生异常", e);
        }
    }

    /**
     * 构建3506请求对象
     *
     * @param nhsa3506      3506实体数据
     * @param lastYearToday 去年今天的日期
     * @param nextYearToday 明年今天的日期
     * @return 构建好的Selinfo3506对象
     */
    private Selinfo3506 buildSelinfo3506(Nhsa3506 nhsa3506, LocalDate lastYearToday, LocalDate nextYearToday) {
        Selinfo3506 selinfo3506 = new Selinfo3506();

        // 设置基本字段
        if (!ObjectUtils.isEmpty(nhsa3506.getMedListCodg())) {
            selinfo3506.setMed_list_codg(nhsa3506.getMedListCodg());
        } else {
            selinfo3506.setMed_list_codg("-");
        }

        selinfo3506.setFixmedins_hilist_id(nhsa3506.getFixmedinsHilistId());
        selinfo3506.setFixmedins_hilist_name(nhsa3506.getFixmedinsHilistName());
        selinfo3506.setFixmedins_bchno(nhsa3506.getFixmedinsBchno());
        selinfo3506.setSetl_id(nhsa3506.getSetlId());
        selinfo3506.setMdtrt_sn(nhsa3506.getMdtrtSn());
        selinfo3506.setPsn_no(nhsa3506.getPsnNo());
        selinfo3506.setPsn_cert_type(nhsa3506.getPsnCertType());
        selinfo3506.setCertno(nhsa3506.getCertno());
        selinfo3506.setPsn_name(nhsa3506.getPsnName());
        selinfo3506.setManu_lotnum(nhsa3506.getManuLotnum());

        // 设置日期字段（带默认值）
        if (!ObjectUtils.isEmpty(nhsa3506.getManuDate())) {
            // 将LocalDate转换为Date
            selinfo3506.setManu_date(java.sql.Date.valueOf(nhsa3506.getManuDate()));
        } else {
            selinfo3506.setManu_date(java.sql.Date.valueOf(lastYearToday));
        }

        if (!ObjectUtils.isEmpty(nhsa3506.getExpyEnd())) {
            // 将LocalDate转换为Date
            selinfo3506.setExpy_end(java.sql.Date.valueOf(nhsa3506.getExpyEnd()));
        } else {
            selinfo3506.setExpy_end(java.sql.Date.valueOf(nextYearToday));
        }

        // 设置其他字段
        selinfo3506.setRx_flag(nhsa3506.getRxFlag());
        selinfo3506.setTrdn_flag(nhsa3506.getTrdnFlag());
        selinfo3506.setMemo(nhsa3506.getMemo());
        selinfo3506.setFinl_trns_pric(nhsa3506.getFinlTrnsPric());
        selinfo3506.setSel_retn_cnt(nhsa3506.getSelRetnCnt());

        // 设置退药时间
        if (!ObjectUtils.isEmpty(nhsa3506.getSelRetnTime())) {
            // 将LocalDateTime转换为Date
            selinfo3506.setSel_retn_time(java.sql.Timestamp.valueOf(nhsa3506.getSelRetnTime()));
        }

        selinfo3506.setSel_retn_opter_name(nhsa3506.getSelRetnOpterName());

        // 设置追溯信息
        if (!ObjectUtils.isEmpty(nhsa3506.getDrugtracinfo())) {
            String[] split = nhsa3506.getDrugtracinfo().split(",");
            List<DrugTracInfo> drugtracinfo = Arrays.stream(split)
                    .map(a -> DrugTracInfo.builder().drug_trac_codg(a.trim()).build())
                    .collect(Collectors.toList());
            selinfo3506.setDrugtracinfo(drugtracinfo);
        }

        // 设置必要字段的默认值
        if (ObjectUtils.isEmpty(selinfo3506.getMdtrt_sn())) {
            selinfo3506.setMdtrt_sn("-");
        }
        if (ObjectUtils.isEmpty(selinfo3506.getManu_lotnum())) {
            selinfo3506.setManu_lotnum("-");
        }

        return selinfo3506;
    }
}
