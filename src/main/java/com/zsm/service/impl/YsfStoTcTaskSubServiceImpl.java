package com.zsm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfStoTcTaskSub;
import com.zsm.mapper.HisDrugDictMapper;
import com.zsm.mapper.Nhsa3505Mapper;
import com.zsm.mapper.YsfStoTcTaskSubMapper;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.YsfStoTcTaskSubQueryDto;
import com.zsm.model.vo.YsfStoTcTaskSubVo;
import com.zsm.service.YsfStoTcTaskSubService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 扫码任务明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Service
public class YsfStoTcTaskSubServiceImpl extends ServiceImpl<YsfStoTcTaskSubMapper, YsfStoTcTaskSub> implements YsfStoTcTaskSubService {
    @Resource
    private YsfStoTcTaskSubMapper ysfStoTcTaskSubMapper;
    @Resource
    private HisDrugDictMapper hisDrugDictMapper;
    @Resource
    private Nhsa3505Mapper nhsa3505Mapper;

    @Override
    public YsfStoTcTaskSub getByTaskIdAndCfmxxh(String taskId, String cfmxxh) {
        LambdaQueryWrapper<YsfStoTcTaskSub> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(YsfStoTcTaskSub::getIdTask, taskId)
                .eq(YsfStoTcTaskSub::getCfmxxh, cfmxxh);
        return ysfStoTcTaskSubMapper.selectOne(wrapper);
    }

    /**
     * 查询扫码任务明细
     *
     * @param id 扫码任务明细主键
     * @return 扫码任务明细
     */
    @Override
    public YsfStoTcTaskSub selectYsfStoTcTaskSubById(Long id) {
        return ysfStoTcTaskSubMapper.selectById(id);
    }

    /**
     * 查询扫码任务明细列表
     *
     * @param queryDto 查询条件
     * @return 扫码任务明细分页数据
     */
    @Override
    public TableDataInfo queryTaskSubList(YsfStoTcTaskSubQueryDto queryDto) {
        try {
            // 设置分页参数
            int pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
            int pageSize = queryDto.getPageSize() == null ? 10 : queryDto.getPageSize();
            Page<YsfStoTcTaskSub> page = new Page<>(pageNum, pageSize);
            
            // 使用MyBatis-Plus分页查询
            IPage<YsfStoTcTaskSub> pageResult = ysfStoTcTaskSubMapper.selectTaskSubList(page, queryDto);

            List<YsfStoTcTaskSubVo> newRecords = new ArrayList<>();
            // 数据转换逻辑，将 YsfStoTcTaskSub 转换为 YsfStoTcTaskSubVo
            for (YsfStoTcTaskSub taskSub : pageResult.getRecords()) {
                YsfStoTcTaskSubVo vo = new YsfStoTcTaskSubVo();
                // 复制基础属性
                BeanUtils.copyProperties(taskSub, vo);
                
                // 通过drugCode关联查询Nhsa3505数据
                if (taskSub.getDrugCode() != null) {
                    try {
                        Nhsa3505 nhsa3505 = nhsa3505Mapper.getFirstByDrugCodeWithSyncStatus(taskSub.getDrugCode());
                        if (nhsa3505 != null) {
                            vo.setMedListCodg(nhsa3505.getMedListCodg());
                            vo.setDrugName(nhsa3505.getFixmedinsHilistName());
                            vo.setProdentpName(nhsa3505.getHisEntpName());
                            // 使用memo字段作为规格信息，在Nhsa3505ServiceImpl中规格信息被存储到memo字段
                            vo.setSpec(nhsa3505.getMemo());
                        }
                    } catch (Exception e) {
                        // 如果查询Nhsa3505数据失败，记录日志但不影响主流程
                        e.printStackTrace();
                    }
                }
                
                newRecords.add(vo);
            }
            
            // 封装结果
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(200);
            tableDataInfo.setMsg("查询成功");
            tableDataInfo.setRows(newRecords); // 返回转换后的记录
            tableDataInfo.setTotal(pageResult.getTotal());

            return tableDataInfo;
        } catch (Exception e) {
            // 记录异常信息
            e.printStackTrace();
            // 返回空结果
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("查询失败：" + e.getMessage());
            tableDataInfo.setRows(null);
            tableDataInfo.setTotal(0);
            return tableDataInfo;
        }
    }
}
