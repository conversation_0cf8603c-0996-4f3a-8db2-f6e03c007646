package com.zsm.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfStoTcStatus;
import com.zsm.mapper.YsfStoTcStatusMapper;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.YsfStoTcStatusQueryDto;
import com.zsm.model.enums.SdTcStatusEnum;
import com.zsm.model.vo.YsfStoTcStatusVo;
import com.zsm.service.YsfStoTcStatusService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 追溯码状态记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class YsfStoTcStatusServiceImpl extends ServiceImpl<YsfStoTcStatusMapper, YsfStoTcStatus> implements YsfStoTcStatusService {

    @Resource
    private YsfStoTcStatusMapper ysfStoTcStatusMapper;

    @Override
    public TableDataInfo queryStatusRecordList(YsfStoTcStatusQueryDto queryDto) {
        try {
            // 设置分页参数
            int pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
            int pageSize = queryDto.getPageSize() == null ? 10 : queryDto.getPageSize();
            Page<YsfStoTcStatus> page = new Page<>(pageNum, pageSize);
            
            // 使用MyBatis-Plus分页查询
            IPage<YsfStoTcStatus> pageResult = ysfStoTcStatusMapper.selectStatusRecordList(page, queryDto);

            // 转换为VO对象，添加业务类型名称和库房名称
            List<YsfStoTcStatusVo> voList = new ArrayList<>();
            for (YsfStoTcStatus status : pageResult.getRecords()) {
                YsfStoTcStatusVo vo = new YsfStoTcStatusVo();
                BeanUtils.copyProperties(status, vo);
                // 设置业务类型名称
                vo.setSdTcStatusName(SdTcStatusEnum.getNameByCode(status.getSdTcStatus()));
                // TODO: 如果有库房服务，可以根据库房ID查询库房名称
                // 这里简单处理，仅展示库房ID
                vo.setStoName("库房-" + status.getIdDept());
                voList.add(vo);
            }

            // 封装结果
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(200);
            tableDataInfo.setMsg("查询成功");
            tableDataInfo.setRows(voList);
            tableDataInfo.setTotal(pageResult.getTotal());

            return tableDataInfo;
        } catch (Exception e) {
            // 记录异常信息
            e.printStackTrace();
            // 返回空结果
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("查询失败：" + e.getMessage());
            tableDataInfo.setRows(null);
            tableDataInfo.setTotal(0);
            return tableDataInfo;
        }
    }
}
