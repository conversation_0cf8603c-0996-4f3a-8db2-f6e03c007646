package com.zsm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfStoTc;
import com.zsm.entity.YsfStoTcStatus;
import com.zsm.mapper.Nhsa3505Mapper;
import com.zsm.mapper.YsfStoTcMapper;
import com.zsm.mapper.YsfStoTcStatusMapper;
import com.zsm.model.vo.TraceabilityCodeInfoVo;
import com.zsm.service.YsfStoTcService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品追溯码 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class YsfStoTcServiceImpl extends ServiceImpl<YsfStoTcMapper, YsfStoTc> implements YsfStoTcService {

    @Resource
    private YsfStoTcStatusMapper ysfStoTcStatusMapper;
    @Resource
    private Nhsa3505Mapper nhsa3505Mapper;

    @Override
    public TraceabilityCodeInfoVo getByDrugtracinfo(String drugtracinfo) {
        TraceabilityCodeInfoVo vo = new TraceabilityCodeInfoVo();

        // 1. 查询追溯码主记录
        LambdaQueryWrapper<YsfStoTc> tcQueryWrapper = Wrappers.lambdaQuery();
        tcQueryWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo);
        YsfStoTc ysfStoTc = baseMapper.selectOne(tcQueryWrapper);

        if (ysfStoTc != null) {
            vo.setTraceabilityCodeInfo(ysfStoTc);

            // 2. 查询该追溯码的所有状态历史记录，按创建时间升序排序
            LambdaQueryWrapper<YsfStoTcStatus> statusQueryWrapper = Wrappers.lambdaQuery();
            statusQueryWrapper.eq(YsfStoTcStatus::getDrugtracinfo, drugtracinfo)
                    .orderByAsc(YsfStoTcStatus::getCreateTime);
            List<YsfStoTcStatus> statusList = ysfStoTcStatusMapper.selectList(statusQueryWrapper);
            vo.setStatusHistory(statusList);

            // 3. 查询药品字典信息
            Nhsa3505 nhsa3505 = nhsa3505Mapper.getFirstByDrugCodeWithSyncStatus(ysfStoTc.getDrugCode());
            if (nhsa3505 != null) {
                vo.setMedListCodg(nhsa3505.getMedListCodg());
                vo.setDrugName(nhsa3505.getFixmedinsHilistName());
                vo.setProdentpName(nhsa3505.getHisEntpName());
                // 使用memo字段作为规格信息，在Nhsa3505ServiceImpl中规格信息被存储到memo字段
                vo.setSpec(nhsa3505.getMemo());
            }

        }

        return vo;
    }
}
