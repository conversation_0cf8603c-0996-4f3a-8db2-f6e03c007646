package com.zsm.common.exception;

/**
 * 业务错误枚举
 * 定义医疗行业追溯码系统常见的业务错误码和错误消息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum BusinessErrorEnum {

    // 通用错误 10000-19999
    PARAM_ERROR(10001, "参数错误"),
    DATA_NOT_FOUND(10002, "数据不存在"),
    DATA_ALREADY_EXISTS(10003, "数据已存在"),
    OPERATION_NOT_ALLOWED(10004, "操作不被允许"),
    SYSTEM_BUSY(10005, "系统繁忙，请稍后重试"),
    INVALID_REQUEST(10006, "无效请求"),
    PERMISSION_DENIED(10007, "权限不足"),

    // 数据库相关错误 20000-29999
    DATABASE_CONNECTION_ERROR(20001, "数据库连接异常"),
    DATABASE_TIMEOUT(20002, "数据库操作超时"),
    DATABASE_CONSTRAINT_VIOLATION(20003, "数据库约束违反"),
    DATABASE_DEADLOCK(20004, "数据库死锁"),
    DATABASE_TRANSACTION_FAILED(20005, "数据库事务失败"),
    DATABASE_DUPLICATE_KEY(20006, "数据库主键冲突"),
    DATABASE_FOREIGN_KEY_VIOLATION(20007, "数据库外键约束违反"),

    // 追溯码系统业务错误 30000-39999
    TRACEABILITY_CODE_NOT_FOUND(30001, "追溯码不存在"),
    TRACEABILITY_CODE_ALREADY_EXISTS(30002, "追溯码已存在"),
    TRACEABILITY_CODE_INVALID(30003, "追溯码格式无效"),
    TRACEABILITY_CODE_EXPIRED(30004, "追溯码已过期"),
    TRACEABILITY_CODE_USED(30005, "追溯码已被使用"),
    TRACEABILITY_BATCH_NOT_FOUND(30006, "追溯批次不存在"),
    TRACEABILITY_PRODUCT_NOT_FOUND(30007, "追溯产品不存在"),
    TRACEABILITY_CHAIN_BROKEN(30008, "追溯链断裂"),
    MEDICAL_DEVICE_NOT_FOUND(30009, "医疗器械信息不存在"),
    MEDICAL_DEVICE_RECALL(30010, "医疗器械已召回"),
    SUPPLIER_INFO_MISSING(30011, "供应商信息缺失"),
    QUALITY_CHECK_FAILED(30012, "质量检查不合格"),

    // HIS系统相关错误 40000-49999
    HIS_CONNECTION_FAILED(40001, "HIS系统连接失败"),
    HIS_AUTHENTICATION_FAILED(40002, "HIS系统认证失败"),
    HIS_PATIENT_NOT_FOUND(40003, "HIS系统患者信息不存在"),
    HIS_DOCTOR_NOT_FOUND(40004, "HIS系统医生信息不存在"),
    HIS_DEPARTMENT_NOT_FOUND(40005, "HIS系统科室信息不存在"),
    HIS_ORDER_NOT_FOUND(40006, "HIS系统医嘱信息不存在"),
    HIS_INVENTORY_ERROR(40007, "HIS系统库存信息异常"),
    HIS_BILLING_ERROR(40008, "HIS系统计费异常"),
    HIS_DATA_SYNC_FAILED(40009, "HIS系统数据同步失败"),
    HIS_INTERFACE_TIMEOUT(40010, "HIS系统接口超时"),
    HIS_DATA_FORMAT_ERROR(40011, "HIS系统数据格式错误"),

    // 第三方接口错误 50000-59999
    THIRD_PARTY_API_UNAVAILABLE(50001, "第三方API服务不可用"),
    THIRD_PARTY_API_TIMEOUT(50002, "第三方API调用超时"),
    THIRD_PARTY_API_AUTH_FAILED(50003, "第三方API认证失败"),
    THIRD_PARTY_API_RATE_LIMIT(50004, "第三方API调用频率超限"),
    THIRD_PARTY_API_RESPONSE_ERROR(50005, "第三方API响应异常"),
    SOAP_SERVICE_UNAVAILABLE(50006, "SOAP服务不可用"),
    SOAP_REQUEST_FORMAT_ERROR(50007, "SOAP请求格式错误"),
    SOAP_RESPONSE_FORMAT_ERROR(50008, "SOAP响应格式错误"),
    SOAP_FAULT_ERROR(50009, "SOAP故障"),
    WSDL_PARSE_ERROR(50010, "WSDL解析失败"),
    DRUG_SUPERVISION_API_ERROR(50011, "药监局接口异常"),
    MEDICAL_INSURANCE_API_ERROR(50012, "医保接口异常"),
    BANK_PAYMENT_API_ERROR(50013, "银行支付接口异常"),
    SMS_SERVICE_ERROR(50014, "短信服务异常"),
    EMAIL_SERVICE_ERROR(50015, "邮件服务异常"),
    LOGISTICS_TRACKING_ERROR(50016, "物流追踪接口异常"),

    // 医疗业务规则错误 60000-69999
    PRESCRIPTION_INVALID(60001, "处方无效"),
    DRUG_INTERACTION_WARNING(60002, "药物相互作用警告"),
    DRUG_ALLERGY_WARNING(60003, "药物过敏警告"),
    DOSAGE_EXCEEDED(60004, "用药剂量超标"),
    MEDICAL_DEVICE_INCOMPATIBLE(60005, "医疗器械不兼容"),
    STERILIZATION_EXPIRED(60006, "灭菌有效期已过"),
    TEMPERATURE_OUT_OF_RANGE(60007, "储存温度超出范围"),
    BATCH_RECALL_NOTICE(60008, "批次召回通知"),
    REGULATORY_COMPLIANCE_FAILED(60009, "法规合规性检查失败");

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误消息
     */
    private final String message;

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     */
    BusinessErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 根据错误码查找枚举
     *
     * @param code 错误码
     * @return 对应的枚举，如果未找到返回null
     */
    public static BusinessErrorEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (BusinessErrorEnum errorEnum : values()) {
            if (errorEnum.getCode().equals(code)) {
                return errorEnum;
            }
        }
        return null;
    }
} 