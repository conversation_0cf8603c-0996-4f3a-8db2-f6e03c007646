package com.zsm.common;

import com.zsm.common.exception.BusinessException;
import com.zsm.model.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 全局异常处理器
 * 统一处理系统异常，返回标准的ApiResult响应格式
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理 @Valid 注解校验失败异常
     * 主要处理 @RequestBody 参数校验失败的情况
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Map<String, String>> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.warn("参数校验失败: {}", e.getMessage());

        Map<String, String> errors = new HashMap<>();

        // 提取所有字段错误信息
        for (FieldError fieldError : e.getBindingResult()
                .getFieldErrors()) {
            String fieldName = fieldError.getField();
            String errorMessage = fieldError.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        }

        // 如果只有一个字段错误，返回该字段的错误信息作为主要消息
        String mainMessage = "参数校验失败";
        if (errors.size() == 1) {
            mainMessage = errors.values()
                    .iterator()
                    .next();
        }

        return ApiResult.error(400, mainMessage, errors);
    }

    /**
     * 处理 @Validated 注解校验失败异常
     * 主要处理方法参数校验失败的情况
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Map<String, String>> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束校验失败: {}", e.getMessage());

        Map<String, String> errors = new HashMap<>();
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();

        for (ConstraintViolation<?> violation : violations) {
            String fieldName = violation.getPropertyPath()
                    .toString();
            String errorMessage = violation.getMessage();
            errors.put(fieldName, errorMessage);
        }

        String mainMessage = "参数校验失败";
        if (errors.size() == 1) {
            mainMessage = errors.values()
                    .iterator()
                    .next();
        }

        return ApiResult.error(400, mainMessage, errors);
    }

    /**
     * 处理表单数据校验失败异常
     * 主要处理 @ModelAttribute 参数校验失败的情况
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Map<String, String>> handleBindException(BindException e) {
        log.warn("绑定校验失败: {}", e.getMessage());

        Map<String, String> errors = new HashMap<>();

        for (FieldError fieldError : e.getBindingResult()
                .getFieldErrors()) {
            String fieldName = fieldError.getField();
            String errorMessage = fieldError.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        }

        String mainMessage = "参数校验失败";
        if (errors.size() == 1) {
            mainMessage = errors.values()
                    .iterator()
                    .next();
        }

        return ApiResult.error(400, mainMessage, errors);
    }

    /**
     * 处理业务异常
     * 处理自定义的业务异常，返回标准的ApiResult响应格式
     */
    @ExceptionHandler(BusinessException.class)
    // @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: code={}, message={}", e.getCode(), e.getMessage());
        
        // 记录异常堆栈信息，便于调试
        if (log.isDebugEnabled()) {
            log.error("业务异常详细信息: ", e);
        }
        
        return ApiResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: ", e);
        return ApiResult.error(500, "系统内部错误：" + e.getMessage());
    }

    /**
     * 处理其他所有异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<Void> handleException(Exception e) {
        log.error("系统异常: ", e);
        return ApiResult.error(500, "系统异常，请联系管理员");
    }
} 