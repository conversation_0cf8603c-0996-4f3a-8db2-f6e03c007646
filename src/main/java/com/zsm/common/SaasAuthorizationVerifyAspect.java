package com.zsm.common;

import com.zsm.common.exception.BusinessException;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.utils.SaasHttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

@Aspect
@Component
public class SaasAuthorizationVerifyAspect {

    public static final ThreadLocal<SaasUserInfoResponse> userInfoThreadLocal = new ThreadLocal<>();
    private static final Logger log = LoggerFactory.getLogger(SaasAuthorizationVerifyAspect.class);

    /**
     * 处理请求前执行
     */
    @Before(value = "@annotation(saasAuthorizationVerify)")
    public void boBefore(JoinPoint joinPoint, SaasAuthorizationVerify saasAuthorizationVerify) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String authorization = request.getHeader("Authorization");
        String url = StringUtils.substring(request.getRequestURI(), 0, 255);
        String logPrefix = "SaasAuthorizationVerifyAspect:";
        log.info(logPrefix + "url:" + url);
        log.info(logPrefix + "Authorization:" + authorization);
        if (ObjectUtils.isEmpty(authorization)) {
            log.error(logPrefix + "Authorization不存在！");
            throw new BusinessException(HttpStatus.UNAUTHORIZED.value(), "令牌不能为空");
        }
        SaasUserInfoResponse saasUserInfoResponse = SaasHttpUtil.getInfo(authorization);
        if (ObjectUtils.isEmpty(saasUserInfoResponse)) {
            log.error(logPrefix + "Authorization不存在！");
            throw new BusinessException(HttpStatus.UNAUTHORIZED.value(), "令牌不能为空");
        }
        if (saasUserInfoResponse.getCode() == HttpStatus.UNAUTHORIZED.value()) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED.value(), "登录状态已过期");
        }
        if (saasUserInfoResponse.getCode() != HttpStatus.OK.value()) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED.value(), "令牌不能为空");
        }
        saasUserInfoResponse.setAuthorization(authorization);
        userInfoThreadLocal.set(saasUserInfoResponse);
    }

    @After("execution(* *(..)) && @annotation(saasAuthorizationVerify)")
    public void after(SaasAuthorizationVerify saasAuthorizationVerify) {
        userInfoThreadLocal.remove(); // 清理ThreadLocal
    }
}
