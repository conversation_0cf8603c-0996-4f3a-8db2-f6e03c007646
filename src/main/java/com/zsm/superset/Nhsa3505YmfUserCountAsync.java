package com.zsm.superset;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.Nhsa3505YmfUserCount;
import com.zsm.mapper.Nhsa3505Mapper;
import com.zsm.mapper.Nhsa3505YmfUserCountMapper;
import com.zsm.utils.SuperSetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 医保3505接口医秒付用户统计异步处理服务
 * <p>
 * 核心业务功能：
 * 1. 统计各医疗机构医秒付用户在药品追溯码上传方面的数据表现
 * 2. 多维度统计分析：按医疗机构、日期、用户维度进行数据聚合
 * 3. 支持历史数据批量统计和当日实时统计两种模式
 * 4. 自动将统计结果同步到医秒付数据库系统
 * <p>
 * 统计指标说明：
 * - totalCount: 总数据条数（有追溯码的记录数）
 * - traceCodeCount: 追溯码总数量（解析逗号分隔的追溯码）
 * - allCntCount: 药品总数量（sel_retn_cnt字段累计）
 * - allDataNumber: 全部数据条数（包含无追溯码的记录）
 * - traceCodeDataNumber: 有效追溯码数据条数
 * <p>
 * 业务价值：
 * - 监控医疗机构药品追溯码上传质量
 * - 分析医秒付用户使用情况
 * - 为医保监管提供数据支持
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component
@Slf4j
public class Nhsa3505YmfUserCountAsync {


    @Resource
    private Nhsa3505Mapper nhsa3505Mapper;


    @Resource
    private Nhsa3505YmfUserCountMapper nhsa3505YmfUserCountMapper;

    /**
     * 同步统计数据到医秒付数据库
     * <p>
     * 功能实现：
     * 1. 查询本地统计表nhsa_3505_ymf_user_count的所有数据
     * 2. 通过HTTP接口批量上传到医秒付系统
     * 3. 实现数据的跨系统同步
     * <p>
     * 业务场景：
     * - 统计完成后的自动数据同步
     * - 手动触发的数据备份
     * - 医秒付系统数据更新
     */
    public void uploadNhsa3505YmfUserCountList() {
        QueryWrapper<Nhsa3505YmfUserCount> qw = new QueryWrapper<>();
        List<Nhsa3505YmfUserCount> list = nhsa3505YmfUserCountMapper.selectList(qw);
        // 移除list元素中的id字段
        list.forEach(item -> item.setId(null));
        log.info("上传药房扫追溯码数据到superset平台:{}", JSONUtil.toJsonStr(list));
        SuperSetUtil.uploadNhsa3505YmfUserCountList(list);
    }

    /**
     * 当日医秒付用户数据统计
     * <p>
     * 业务逻辑：
     * 1. 获取当前日期，格式化为yyyy-MM-dd
     * 2. 遍历所有医疗机构
     * 3. 对每个医疗机构，查找当日有操作的医秒付用户
     * 4. 对每个用户，统计其当日的各项指标数据
     * 5. 保存或更新统计结果到数据库
     * 6. 自动上传统计数据到医秒付系统
     * <p>
     * 适用场景：
     * - 定时任务每日执行
     * - 实时监控当日数据
     * - 及时发现异常情况
     */
    public void nhsa3505YmfUserCountToday() {
        String datetime = LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String date = LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<Nhsa3505> medicalList = this.getNhsa3505MedicalList();
        for (Nhsa3505 nhsa3505 : medicalList) {
            String medicalCode = nhsa3505.getMedicalCode();
            String medicalName = nhsa3505.getMedicalName();
            List<Nhsa3505> ymfUserList = this.getNhsa3505YmfUserList(medicalCode, date);
            for (Nhsa3505 ymfUser : ymfUserList) {
                Long ymfUserId = ymfUser.getYmfUserId();
                String ymfUserName = ymfUser.getYmfUserName();
                String ymfNickName = ymfUser.getYmfNickName();
                Nhsa3505YmfUserCount count = new Nhsa3505YmfUserCount();
                count.setYmfUserId(ymfUserId);
                count.setYmfUserName(ymfUserName);
                count.setYmfNickName(ymfNickName);
                count.setMedicalCode(medicalCode);
                count.setMedicalName(medicalName);
                count.setCountDate(date);
                count.setTotalCount(this.getNhsa3505Total(medicalCode, date, ymfUserId));
                count.setTraceCodeCount(this.getNhsa3505TraceCodeTotal(medicalCode, date, ymfUserId));
                count.setAllCntCount(this.getNhsa3505AllCnt(medicalCode, date, ymfUserId));
                count.setAllDataNumber(this.getNhsa3505AllDataNumber(medicalCode, date, ymfUserId));
                count.setTraceCodeDataNumber(this.getNhsa3505TraceCodeDataNumber(medicalCode, date, ymfUserId));
                this.saveOrUpdateNhsa3505YmfUserCount(count);
            }
        }
        this.uploadNhsa3505YmfUserCountList();
    }

    /**
     * 历史数据全量统计
     * <p>
     * 业务逻辑：
     * 1. 遍历所有医疗机构
     * 2. 对每个医疗机构，获取所有有数据的日期列表
     * 3. 对每个日期，查找当日有操作的医秒付用户
     * 4. 对每个用户每日的数据进行统计分析
     * 5. 计算多个维度的统计指标
     * 6. 批量保存统计结果
     * 7. 最后统一上传到医秒付系统
     * <p>
     * 适用场景：
     * - 系统初始化数据补录
     * - 历史数据重新统计
     * - 数据修复和校正
     * <p>
     * 性能考虑：
     * - 使用异步处理避免阻塞
     * - 按日期倒序处理，优先处理最新数据
     */
    public void nhsa3505YmfUserCount() {
        List<Nhsa3505> medicalList = this.getNhsa3505MedicalList();
        for (Nhsa3505 nhsa3505 : medicalList) {
            String medicalCode = nhsa3505.getMedicalCode();
            String medicalName = nhsa3505.getMedicalName();
            List<Object> dateList = this.getNhsa3505DateList(medicalCode);
            for (Object dateObj : dateList) {
                String date = String.valueOf(dateObj);
                List<Nhsa3505> ymfUserList = this.getNhsa3505YmfUserList(medicalCode, date);
                for (Nhsa3505 ymfUser : ymfUserList) {
                    Long ymfUserId = ymfUser.getYmfUserId();
                    String ymfUserName = ymfUser.getYmfUserName();
                    String ymfNickName = ymfUser.getYmfNickName();
                    Nhsa3505YmfUserCount count = new Nhsa3505YmfUserCount();
                    count.setYmfUserId(ymfUserId);
                    count.setYmfUserName(ymfUserName);
                    count.setYmfNickName(ymfNickName);
                    count.setMedicalCode(medicalCode);
                    count.setMedicalName(medicalName);
                    count.setCountDate(date);
                    count.setTotalCount(this.getNhsa3505Total(medicalCode, date, ymfUserId));
                    count.setTraceCodeCount(this.getNhsa3505TraceCodeTotal(medicalCode, date, ymfUserId));
                    count.setAllCntCount(this.getNhsa3505AllCnt(medicalCode, date, ymfUserId));
                    count.setAllDataNumber(this.getNhsa3505AllDataNumber(medicalCode, date, ymfUserId));
                    count.setTraceCodeDataNumber(this.getNhsa3505TraceCodeDataNumber(medicalCode, date, ymfUserId));
                    this.saveOrUpdateNhsa3505YmfUserCount(count);
                }
            }
        }
        this.uploadNhsa3505YmfUserCountList();
    }

    /**
     * 根据日期范围统计医秒付用户数据
     * @param startDate 开始日期，格式：yyyy-MM-dd，为null时默认为今天
     * @param endDate 结束日期，格式：yyyy-MM-dd，为null时默认为今天
     */
    public void nhsa3505YmfUserCount(String startDate, String endDate) {
        // 如果没有传入日期参数，默认统计今天的数据
        if (startDate == null || startDate.trim().isEmpty()) {
            startDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        if (endDate == null || endDate.trim().isEmpty()) {
            endDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        
        List<Nhsa3505> medicalList = this.getNhsa3505MedicalList();
        for (Nhsa3505 nhsa3505 : medicalList) {
            String medicalCode = nhsa3505.getMedicalCode();
            String medicalName = nhsa3505.getMedicalName();
            List<Object> dateList = this.getNhsa3505DateListByRange(medicalCode, startDate, endDate);
            for (Object dateObj : dateList) {
                String date = String.valueOf(dateObj);
                List<Nhsa3505> ymfUserList = this.getNhsa3505YmfUserList(medicalCode, date);
                for (Nhsa3505 ymfUser : ymfUserList) {
                    Long ymfUserId = ymfUser.getYmfUserId();
                    String ymfUserName = ymfUser.getYmfUserName();
                    String ymfNickName = ymfUser.getYmfNickName();
                    Nhsa3505YmfUserCount count = new Nhsa3505YmfUserCount();
                    count.setYmfUserId(ymfUserId);
                    count.setYmfUserName(ymfUserName);
                    count.setYmfNickName(ymfNickName);
                    count.setMedicalCode(medicalCode);
                    count.setMedicalName(medicalName);
                    count.setCountDate(date);
                    count.setTotalCount(this.getNhsa3505Total(medicalCode, date, ymfUserId));
                    count.setTraceCodeCount(this.getNhsa3505TraceCodeTotal(medicalCode, date, ymfUserId));
                    count.setAllCntCount(this.getNhsa3505AllCnt(medicalCode, date, ymfUserId));
                    count.setAllDataNumber(this.getNhsa3505AllDataNumber(medicalCode, date, ymfUserId));
                    count.setTraceCodeDataNumber(this.getNhsa3505TraceCodeDataNumber(medicalCode, date, ymfUserId));
                    this.saveOrUpdateNhsa3505YmfUserCount(count);
                }
            }
        }
        
        // 只上传指定日期范围内的数据
        QueryWrapper<Nhsa3505YmfUserCount> qw = new QueryWrapper<>();
        qw.ge("count_date", startDate);
        qw.le("count_date", endDate);
        List<Nhsa3505YmfUserCount> list = nhsa3505YmfUserCountMapper.selectList(qw);
        // 移除list元素中的id字段
        list.forEach(item -> item.setId(null));
        log.info("上传药房扫追溯码数据到superset平台(日期范围：{} 至 {}):{}", startDate, endDate, JSONUtil.toJsonStr(list));
        SuperSetUtil.uploadNhsa3505YmfUserCountList(list);
    }

    /**
     * 保存或更新统计数据
     * <p>
     * 实现逻辑：
     * 1. 根据医疗机构代码、统计日期、医秒付用户ID查询现有记录
     * 2. 如果不存在则新增记录
     * 3. 如果已存在则更新现有记录（保留主键ID）
     * 4. 使用synchronized确保线程安全
     * <p>
     * 数据一致性：
     * - 防止重复插入相同维度的统计数据
     * - 支持数据更新和修正
     *
     * @param count 统计数据对象
     */
    private synchronized void saveOrUpdateNhsa3505YmfUserCount(Nhsa3505YmfUserCount count) {
        QueryWrapper<Nhsa3505YmfUserCount> qw = new QueryWrapper<>();
        qw.eq("medical_code", count.getMedicalCode());
        qw.eq("count_date", count.getCountDate());
        if (!ObjectUtils.isEmpty(count.getYmfUserId())) {
            qw.eq("ymf_user_id", count.getYmfUserId());
        }
        List<Nhsa3505YmfUserCount> nhsa3505YmfUserCounts = nhsa3505YmfUserCountMapper.selectList(qw);
        if (ObjectUtils.isEmpty(nhsa3505YmfUserCounts)) {
            nhsa3505YmfUserCountMapper.insert(count);
        } else {
            Nhsa3505YmfUserCount data = nhsa3505YmfUserCounts.get(0);
            BeanUtil.copyProperties(count, data, "id");
            nhsa3505YmfUserCountMapper.updateById(data);
        }
    }

    /**
     * 获取所有医疗机构列表
     * <p>
     * 实现方式：
     * - 从nhsa_3505表中按medical_code分组
     * - 去重获取所有有数据的医疗机构
     *
     * @return 医疗机构列表
     */
    private List<Nhsa3505> getNhsa3505MedicalList() {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.groupBy("medical_code");
        return nhsa3505Mapper.selectList(qw);
    }

    /**
     * 获取指定医疗机构的所有数据日期列表
     * <p>
     * 实现逻辑：
     * 1. 查询指定医疗机构的所有数据记录
     * 2. 按创建时间的日期部分分组
     * 3. 按日期倒序排列（最新日期优先）
     *
     * @param medicalCode 医疗机构代码
     * @return 日期列表（字符串格式）
     */
    private List<Object> getNhsa3505DateList(String medicalCode) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.select("date(create_time)");
        qw.groupBy("date(create_time)");
        qw.orderByDesc("date(create_time)");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return objects;
    }

    /**
     * 获取指定医疗机构指定日期的医秒付用户列表
     * <p>
     * 查询条件：
     * - 指定医疗机构代码
     * - 指定创建日期
     * - 按医秒付用户ID分组去重
     *
     * @param medicalCode 医疗机构代码
     * @param date        统计日期
     * @return 医秒付用户列表
     */
    private List<Nhsa3505> getNhsa3505YmfUserList(String medicalCode, String date) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.eq("date(create_time)", date);
        qw.groupBy("ymf_user_id");
        return nhsa3505Mapper.selectList(qw);

    }


    /**
     * 统计指定条件下的总数据条数
     * <p>
     * 统计范围：
     * - 指定医疗机构、日期、医秒付用户的所有数据记录
     * - 包含有追溯码和无追溯码的所有记录
     *
     * @param medicalCode 医疗机构代码
     * @param date        统计日期
     * @param ymfUserId   医秒付用户ID
     * @return 总数据条数
     */
    private Integer getNhsa3505AllDataNumber(String medicalCode, String date, Long ymfUserId) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.eq(!ObjectUtils.isEmpty(ymfUserId), "ymf_user_id", ymfUserId);
        qw.eq("date(create_time)", date);
        return Math.toIntExact(nhsa3505Mapper.selectCount(qw));
    }

    /**
     * 统计有效追溯码数据条数
     * <p>
     * 统计条件：
     * - 指定医疗机构、日期、医秒付用户
     * - drugTracInfo字段不为空且不为空字符串
     * <p>
     * 业务意义：衡量数据质量，有追溯码的记录才是有效数据
     *
     * @param medicalCode 医疗机构代码
     * @param date        统计日期
     * @param ymfUserId   医秒付用户ID
     * @return 有效追溯码数据条数
     */
    private Integer getNhsa3505TraceCodeDataNumber(String medicalCode, String date, Long ymfUserId) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.eq(!ObjectUtils.isEmpty(ymfUserId), "ymf_user_id", ymfUserId);
        qw.eq("date(create_time)", date);
        qw.ne("drug_trac_info", "");
        qw.isNotNull("drug_trac_info");
        return Math.toIntExact(nhsa3505Mapper.selectCount(qw));
    }

    /**
     * 统计总药品数量
     * <p>
     * 计算逻辑：
     * - 对sel_retn_cnt字段进行求和
     * - sel_retn_cnt表示药品的销售/退货数量
     * <p>
     * 业务价值：了解用户处理的药品总量
     *
     * @param medicalCode 医疗机构代码
     * @param date        统计日期
     * @param ymfUserId   医秒付用户ID
     * @return 药品总数量
     */
    private Integer getNhsa3505AllCnt(String medicalCode, String date, Long ymfUserId) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.select("sum(sel_retn_cnt)");
        qw.eq("medical_code", medicalCode);
        qw.eq(!ObjectUtils.isEmpty(ymfUserId), "ymf_user_id", ymfUserId);
        qw.eq("date(create_time)", date);
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return this.getInteger(objects);
    }


    /**
     * 统计有追溯码的药品总数量
     * <p>
     * 计算逻辑：
     * - 仅统计有追溯码的记录（drugTracInfo不为空）
     * - 对这些记录的sel_retn_cnt字段求和
     * <p>
     * 业务意义：衡量有效药品追溯的数量规模
     * <p>
     * SQL逻辑：
     * SELECT sum(sel_retn_cnt) FROM nhsa_3505
     * WHERE medical_code = ? AND date(create_time) = ?
     * AND drug_trac_info IS NOT NULL AND drug_trac_info != ''
     *
     * @param medicalCode 医疗机构代码
     * @param date        统计日期
     * @param ymfUserId   医秒付用户ID
     * @return 有追溯码的药品数量总和
     */
    private Integer getNhsa3505Total(String medicalCode, String date, Long ymfUserId) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.select("sum(sel_retn_cnt)");
        qw.eq("medical_code", medicalCode);
        qw.eq("date(create_time)", date);
        qw.eq(!ObjectUtils.isEmpty(ymfUserId), "ymf_user_id", ymfUserId);
        qw.ne("drug_trac_info", "");
        qw.isNotNull("drug_trac_info");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return this.getInteger(objects);
    }

    /**
     * 统计追溯码总数量
     * <p>
     * 计算逻辑：
     * 1. 针对每条有追溯码的记录
     * 2. 计算drug_trac_info字段中逗号的个数+1（即追溯码个数）
     * 3. 对所有记录的追溯码个数求和
     * 4. 仅统计同步状态为'1'的记录
     * <p>
     * SQL表达式：
     * SUM(CASE WHEN drug_trac_info IS NOT NULL AND TRIM(drug_trac_info) != ''
     * THEN LENGTH(drug_trac_info) - LENGTH(REPLACE(drug_trac_info, ',', '')) + 1
     * ELSE 0 END)
     * <p>
     * 业务价值：
     * - 精确统计追溯码的实际数量
     * - 一条记录可能包含多个追溯码（逗号分隔）
     * - 评估数据的细粒度质量
     *
     * @param medicalCode 医疗机构代码
     * @param date        统计日期
     * @param ymfUserId   医秒付用户ID
     * @return 追溯码总数量
     */
    private Integer getNhsa3505TraceCodeTotal(String medicalCode, String date, Long ymfUserId) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.select("SUM(CASE WHEN drug_trac_info IS NOT NULL AND TRIM( drug_trac_info ) != '' THEN LENGTH( drug_trac_info ) - LENGTH(REPLACE ( drug_trac_info, ',', '' )) + 1 ELSE 0 END )");
        qw.eq("medical_code", medicalCode);
        qw.eq("hsa_sync_status", "1");
        qw.eq(!ObjectUtils.isEmpty(ymfUserId), "ymf_user_id", ymfUserId);
        qw.eq("date(create_time)", date);
        qw.ne("drug_trac_info", "");
        qw.isNotNull("drug_trac_info");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return this.getInteger(objects);
    }

    /**
     * 获取指定医疗机构在日期范围内的数据日期列表
     * @param medicalCode 医疗机构代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期列表（字符串格式）
     */
    private List<Object> getNhsa3505DateListByRange(String medicalCode, String startDate, String endDate) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.select("date(create_time)");
        qw.ge("date(create_time)", startDate);
        qw.le("date(create_time)", endDate);
        qw.groupBy("date(create_time)");
        qw.orderByDesc("date(create_time)");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return objects;
    }

    /**
     * 数据类型转换工具方法
     * <p>
     * 功能说明：
     * 1. 将数据库查询结果转换为Integer类型
     * 2. 处理BigDecimal类型的数值结果
     * 3. 安全处理null值和异常类型
     * <p>
     * 异常处理：
     * - 如果结果为BigDecimal，转换为int
     * - 如果结果为其他类型，记录错误并返回0
     * - 如果结果为null或空，返回0
     *
     * @param objects 数据库查询结果列表
     * @return 转换后的整数值
     */
    private Integer getInteger(List<Object> objects) {
        if (!ObjectUtils.isEmpty(objects) && objects.get(0) instanceof BigDecimal) {
            BigDecimal bigDecimalObject = (BigDecimal) objects.get(0);
            return bigDecimalObject.intValue(); // 如果您希望确保没有精度损失
        } else if (!ObjectUtils.isEmpty(objects) && objects.get(0) != null) {
            System.err.println("第一个元素不是BigDecimal类型: " + objects.get(0)
                    .getClass()
                    .getSimpleName());
            return 0; // 或者其他适当的默认值
        }
        return 0; // 或者根据上下文返回其他适当的默认值
    }
}
