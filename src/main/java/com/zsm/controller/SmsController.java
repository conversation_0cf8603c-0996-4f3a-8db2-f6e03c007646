package com.zsm.controller;

import com.zsm.model.ApiResult;
import com.zsm.utils.SmsUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 短信发送控制器
 * 提供短信发送测试接口
 * 
 * <AUTHOR>
 * @date 2025/08/06
 */
@Tag(name = "短信发送", description = "短信发送测试接口")
@Slf4j
@RestController
@RequestMapping("/sms")
public class SmsController {

    @Autowired
    private SmsUtil smsUtil;

    /**
     * 短信发送测试接口
     * 
     * @param phone   手机号
     * @param message 短信内容
     * @return 发送结果
     */
    @Operation(summary = "短信发送测试", description = "发送短信到指定手机号")
    @PostMapping("/test")
    public ApiResult<String> testSendSms(@RequestParam String phone, @RequestParam String message) {
        log.info("收到短信发送测试请求: phone={}, message={}", phone, message);
        
        try {
            String result = smsUtil.sendSms(phone, message);
            log.info("短信发送结果: {}", result);
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("短信发送异常: {}", e.getMessage(), e);
            return ApiResult.error("短信发送失败: " + e.getMessage());
        }
    }
}