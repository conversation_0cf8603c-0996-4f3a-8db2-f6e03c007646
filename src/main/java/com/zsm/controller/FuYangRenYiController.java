package com.zsm.controller;

import com.zsm.common.SaasAuthorizationVerify;
import com.zsm.model.ApiResult;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.*;
import com.zsm.model.vo.*;
import com.zsm.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 阜阳人医 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Deprecated
@Tag(name = "阜阳人医", description = "门诊发药业务接口")
// @RestController
// @RequestMapping("/fuYangRenYi")
public class FuYangRenYiController {

    @Resource
    private FuYangRenYiService fuYangRenYiService;
    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;
    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;
    @Resource
    private YsfStoTcStatusService ysfStoTcStatusService;
    @Resource
    private YsfStoTcService ysfStoTcService;
    @Resource
    private DispensingService dispensingService;
    @Resource
    private ReturnDrugService returnDrugService;
    @Resource
    private YsfService ysfService;

    /**
     * 查询门诊处方接口
     * 支持根据fg_dps参数区分查询发药和退药数据：
     * - fg_dps=0：发药查询，会应用发药业务的过滤逻辑（过滤已完成扫码任务）
     * - fg_dps=1：退药查询，不会应用发药业务的过滤逻辑，直接返回原始数据
     *
     * @param queryDto 查询参数，包含fg_dps字段用于区分发药/退药
     * @return 门诊处方数据
     */
    @PostMapping("/queryOutpatientPrescription")
    @Operation(summary = "查询门诊处方", description = "支持发药和退药数据查询，根据fg_dps参数区分处理逻辑")
    @SaasAuthorizationVerify
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(@RequestBody OutpatientPrescriptionQueryDto queryDto) {
        return fuYangRenYiService.queryOutpatientPrescription(queryDto);
    }

    /**
     * 住院领药单接口
     * 住院发药走的定时任务,没有走查询接口
     * @param queryDto 查询参数
     * @return 住院处方数据
     */
    @PostMapping("/queryInpatientPrescription")
    @Operation(summary = "住院领药单接口")
    public ApiResult<List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryInpatientPrescription(@RequestBody InpatientPrescriptionQueryDto queryDto) {
        return fuYangRenYiService.queryInpatientPrescription(queryDto);
    }

    /**
     * 药品追溯码扫描与上传接口
     *
     * @param request 追溯码上传请求
     * @return 上传结果
     */
    @PostMapping("/uploadScans")
    @Operation(summary = "药品追溯码扫描上传", description = "将本次采集的所有药品追溯码信息批量提交")
    @SaasAuthorizationVerify
    public ApiResult<TraceabilityUploadResultVo> uploadScans(@RequestBody TraceabilityUploadDto request) {
        // 调用服务处理追溯码上传
        return ysfService.uploadScans(request);
    }

    /**
     * 取消扫码任务
     *
     * @param taskId 任务ID
     * @return 取消结果
     */
    @PostMapping("/cancel/{taskId}")
    @Operation(summary = "取消扫码任务", description = "根据任务ID取消未完成的扫码任务")
    @SaasAuthorizationVerify
    public ApiResult<String> cancelTask(@PathVariable Long taskId) {
        return ysfService.cancelTask(taskId);
    }

    /**
     * 查询发药单列表
     *
     * @param queryDto 查询参数
     * @return 发药单列表
     */
    @GetMapping("/YsfStoDps/list")
    @Operation(summary = "查询发药单列表", description = "根据条件查询发药单列表信息，包含分页")
    @SaasAuthorizationVerify
    public TableDataInfo listDispensingRecords(DispensingRecordQueryDto queryDto) {
        return ysfService.queryDispensingRecords(queryDto);
    }

    /**
     * 查询发药单明细列表
     *
     * @param queryDto 查询参数
     * @return 发药单明细列表
     */
    @GetMapping("/YsfStoDpsSub/list")
    @Operation(summary = "查询发药单明细列表", description = "根据发药单ID查询发药单明细列表，支持按药品名称/编码、是否已采集等条件过滤")
    @SaasAuthorizationVerify
    public TableDataInfo listDispensingRecordDetails(DispensingRecordDetailQueryDto queryDto) {
        return ysfService.queryDispensingRecordDetails(queryDto);
    }

    /**
     * 查询扫码任务列表
     *
     * @param queryDto 查询参数
     * @return 扫码任务列表
     */
    @GetMapping("/YsfStoTcTask/list")
    @Operation(summary = "查询扫码任务列表", description = "支持按任务状态、患者姓名、业务单号、任务类型、创建时间范围等查询")
    @SaasAuthorizationVerify
    public TableDataInfo listTasks(@Validated YsfStoTcTaskQueryDto queryDto) {
        return ysfStoTcTaskService.queryTaskList(queryDto);
    }

    /**
     * 查询扫码任务明细列表
     *
     * @param queryDto 查询参数
     * @return 扫码任务明细列表
     */
    @GetMapping("/YsfStoTcTaskSub/list")
    @Operation(summary = "查询扫码任务明细列表", description = "根据任务ID查询扫码任务明细列表，支持按药品名称/编码、是否已扫码等条件过滤")
    @SaasAuthorizationVerify
    public TableDataInfo listTaskDetails(@Validated YsfStoTcTaskSubQueryDto queryDto) {
        return ysfStoTcTaskSubService.queryTaskSubList(queryDto);
    }

    /**
     * 单个追溯码及其生命周期查询
     *
     * @param drugtracinfo 追溯码
     * @return 追溯码信息和生命周期记录
     */
    @GetMapping("/YsfStoTc/getByDrugtracinfo/{drugtracinfo}")
    @Operation(summary = "查询单个追溯码及其生命周期", description = "根据追溯码查询其基本信息和状态变更历史")
    @SaasAuthorizationVerify
    public ApiResult<TraceabilityCodeInfoVo> getByDrugtracinfo(@PathVariable("drugtracinfo") String drugtracinfo) {
        // 调用服务查询追溯码信息
        TraceabilityCodeInfoVo result = ysfStoTcService.getByDrugtracinfo(drugtracinfo);
        if (result.getTraceabilityCodeInfo() == null) {
            return ApiResult.error("未找到该追溯码信息");
        }
        return ApiResult.success(result);
    }

    /**
     * 查询追溯码流转记录列表
     *
     * @param queryDto 查询参数
     * @return 流转记录列表
     */
    @GetMapping("/YsfStoTcStatus/list")
    @Operation(summary = "查询追溯码流转记录", description = "根据条件查询追溯码流转记录列表")
    @SaasAuthorizationVerify
    public TableDataInfo listTcStatusRecords(@Validated YsfStoTcStatusQueryDto queryDto) {
        return ysfStoTcStatusService.queryStatusRecordList(queryDto);
    }

    /**
     * 根据业务子ID查询发药明细
     *
     * @param bizSubId 业务子ID，通常是发药单明细ID或处方明细ID
     * @return 发药明细信息
     */
    @GetMapping("/dispensing/detailsByBizSubId")
    @Operation(summary = "根据业务子ID查询发药明细", description = "通过业务子ID（如发药单明细ID）直接查询关联的发药信息")
    @SaasAuthorizationVerify
    public ApiResult<DispensingDetailVo> getDispensingDetailsByBizSubId(@RequestParam("bizSubId") String bizSubId) {
        // 调用服务获取发药明细
        DispensingDetailVo detail = dispensingService.getDetailByBizSubId(bizSubId);
        if (detail == null) {
            return ApiResult.error("未找到相关发药明细信息");
        }
        return ApiResult.success(detail);
    }

    /**
     * 退药上传
     *
     * @param uploadDto 退药上传数据
     * @return 退药上传结果
     */
    @PostMapping("/return")
    @Operation(summary = "退药上传", description = "上传退药信息进行退药处理")
    @SaasAuthorizationVerify
    public ApiResult<ReturnDrugResultVo> returnDrug(@RequestBody @Validated TraceabilityUploadDto uploadDto) {
        return returnDrugService.returnDrug(uploadDto);
    }

    /**
     * 处理指定时间段的住院拆零确认数据
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 处理结果详情，包含HIS接口获取数量和确认的拆药数量
     */
    @GetMapping("/processTimeSlot")
    @Operation(summary = "处理指定时间段的住院拆零确认数据", description = "手动触发指定时间段的住院拆零确认处理，返回详细的处理统计信息")
    public ApiResult<ProcessingResultVo> processTimeSlot(@RequestParam(value = "startTime", defaultValue = "2025-06-21 13:52:00") String startTime,
                                                         @RequestParam(value = "endTime", defaultValue = "2025-06-21 14:53:00") String endTime) {
        try {
            // 解析时间参数
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);
            
            // 直接调用服务处理并返回详细结果（包含异常处理）
            return dispensingService.processInpatientDispenseConfirmationByTimeRange(start, end);
        } catch (Exception e) {
            // 仅处理时间解析异常，返回参数错误信息
            ProcessingResultVo errorResult = ProcessingResultVo.builder()
                    .success(false)
                    .message("时间参数解析异常：" + e.getMessage())
                    .hisDataCount(0)
                    .splitDrugCount(0)
                    .confirmedCount(0)
                    .skippedCount(0)
                    .failedCount(0)
                    .accountDetails(new ArrayList<>())
                    .build();
            return ApiResult.error(400, "参数错误，请检查时间格式（yyyy-MM-dd HH:mm:ss）：" + e.getMessage(), errorResult);
        }
    }
}
