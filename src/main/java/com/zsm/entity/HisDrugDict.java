package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 药品字典表实体类
 * 基于JSON数据结构设计
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@TableName("his_drug_dictionary")
@Schema(description = "药品字典表")
public class HisDrugDict implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * HIS药品编码
     */
    @Schema(description = "HIS药品编码")
    @Size(max = 100, message = "HIS药品编码长度不能超过100个字符")
    @TableField("his_drug_code")
    private String hisDrugCode;

    /**
     * HIS药品生产厂家编码
     */
    @Schema(description = "HIS药品生产厂家编码")
    @Size(max = 100, message = "HIS药品生产厂家编码长度不能超过100个字符")
    @TableField("his_drug_manufacturer_code")
    private String hisDrugManufacturerCode;

    /**
     * 药品统一编码
     */
    @Schema(description = "药品统一编码")
    @Size(max = 100, message = "药品统一编码长度不能超过100个字符")
    @TableField("drug_unino")
    private String drugUnino;

    /**
     * HIS药品通用名称
     */
    @Schema(description = "HIS药品通用名称")
    @Size(max = 200, message = "HIS药品通用名称长度不能超过200个字符")
    @TableField("his_drug_country_name")
    private String hisDrugCountryName;

    /**
     * HIS药品名称
     */
    @Schema(description = "HIS药品名称")
    @NotBlank(message = "HIS药品名称不能为空")
    @Size(max = 200, message = "HIS药品名称长度不能超过200个字符")
    @TableField("his_drug_name")
    private String hisDrugName;

    /**
     * 剂型
     */
    @Schema(description = "剂型")
    @Size(max = 50, message = "剂型长度不能超过50个字符")
    @TableField("dosform")
    private String dosform;

    /**
     * HIS药品生产厂家名称
     */
    @Schema(description = "HIS药品生产厂家名称")
    @Size(max = 200, message = "HIS药品生产厂家名称长度不能超过200个字符")
    @TableField("his_drug_manufacturer_name")
    private String hisDrugManufacturerName;

    /**
     * 批准文号
     */
    @Schema(description = "批准文号")
    @Size(max = 100, message = "批准文号长度不能超过100个字符")
    @TableField("aprvno")
    private String aprvno;

    /**
     * HIS药品规格
     */
    @Schema(description = "HIS药品规格")
    @Size(max = 200, message = "HIS药品规格长度不能超过200个字符")
    @TableField("his_drug_spec")
    private String hisDrugSpec;

    /**
     * HIS药品单位
     */
    @Schema(description = "HIS药品单位")
    @Size(max = 20, message = "HIS药品单位长度不能超过20个字符")
    @TableField("his_drug_unit")
    private String hisDrugUnit;

    /**
     * HIS包装数量
     */
    @Schema(description = "HIS包装数量")
    @Size(max = 50, message = "HIS包装数量长度不能超过50个字符")
    @TableField("his_pac")
    private String hisPac;

    /**
     * HIS包装单位
     */
    @Schema(description = "HIS包装单位")
    @Size(max = 20, message = "HIS包装单位长度不能超过20个字符")
    @TableField("his_pac_unit")
    private String hisPacUnit;

    /**
     * HIS采购价格
     */
    @Schema(description = "HIS采购价格")
    @DecimalMin(value = "0.00", message = "HIS采购价格不能小于0")
    @DecimalMax(value = "99999999.99", message = "HIS采购价格不能超过99999999.99")
    @TableField("his_purchase_price")
    private BigDecimal hisPurchasePrice;


} 
