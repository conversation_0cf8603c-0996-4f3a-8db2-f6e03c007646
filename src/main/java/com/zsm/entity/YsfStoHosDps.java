package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 住院发药单实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_hos_dps")
@Schema(description = "住院发药单")
public class YsfStoHosDps implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id_dps_hos", type = IdType.AUTO)
    private Long idDpsHos;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    @Size(max = 100, message = "来源ID长度不能超过100个字符")
    @TableField("id_plan_sub")
    private String idPlanSub;

    /**
     * 入住记录唯一标识
     */
    @Schema(description = "入住记录唯一标识")
    @Size(max = 100, message = "入住记录唯一标识长度不能超过100个字符")
    @TableField("id_adsn")
    private String idAdsn;

    /**
     * 原发药单ID;用于退药单,原发药单明细id
     */
    @Schema(description = "原发药单ID;用于退药单,原发药单明细id")
    @Size(max = 100, message = "原发药单ID;用于退药单,原发药单明细id长度不能超过100个字符")
    @TableField("ori_id")
    private String oriId;

    /**
     * 提交单号
     */
    @Schema(description = "提交单号")
    @Size(max = 100, message = "提交单号长度不能超过100个字符")
    @TableField("cd_submit_no")
    private String cdSubmitNo;

    /**
     * 医嘱类别;1临时 2长期;phis.ins.drord_type
     */
    @Schema(description = "医嘱类别;1临时 2长期;phis.ins.drord_type")
    @Size(max = 100, message = "医嘱类别;1临时 2长期;phis.ins.drord_type长度不能超过100个字符")
    @TableField("sd_classify")
    private String sdClassify;

    /**
     * 费用明细ID
     */
    @Schema(description = "费用明细ID")
    @Size(max = 100, message = "费用明细ID长度不能超过100个字符")
    @TableField("id_fee")
    private String idFee;

    /**
     * 费用名称
     */
    @Schema(description = "费用名称")
    @Size(max = 100, message = "费用名称长度不能超过100个字符")
    @TableField("na_fee")
    private String naFee;

    /**
     * 药品ID（Pro）
     */
    @Schema(description = "药品ID（Pro）")
    @Size(max = 100, message = "药品ID（Pro）长度不能超过100个字符")
    @TableField("id_med_pro")
    private String idMedPro;

    /**
     * 发药病区
     */
    @Schema(description = "发药病区")
    @Size(max = 100, message = "发药病区长度不能超过100个字符")
    @TableField("id_war")
    private String idWar;

    /**
     * 类型;0常规，1 出院带药，2 急诊用药
     */
    @Schema(description = "类型;0常规，1 出院带药，2 急诊用药")
    @Size(max = 100, message = "类型;0常规，1 出院带药，2 急诊用药长度不能超过100个字符")
    @TableField("sd_type")
    private String sdType;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @TableField("price_sale")
    private BigDecimal priceSale;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @TableField("sel_retn_cnt")
    private Integer selRetnCnt;

    /**
     * 实发数量
     */
    @Schema(description = "实发数量")
    @TableField("amount_dps")
    private Integer amountDps;

    /**
     * 金额
     */
    @Schema(description = "金额")
    @TableField("amt_total")
    private BigDecimal amtTotal;

    /**
     * 实发金额
     */
    @Schema(description = "实发金额")
    @TableField("amt_total_dps")
    private BigDecimal amtTotalDps;

    /**
     * 包装单位
     */
    @Schema(description = "包装单位")
    @Size(max = 100, message = "包装单位长度不能超过100个字符")
    @TableField("unit_sale")
    private String unitSale;

    /**
     * 包装系数
     */
    @Schema(description = "包装系数")
    @TableField("unit_sale_factor")
    private Integer unitSaleFactor;

    /**
     * 取整策略
     */
    @Schema(description = "取整策略")
    @Size(max = 100, message = "取整策略长度不能超过100个字符")
    @TableField("sd_round")
    private String sdRound;

    /**
     * 发药方式;rbmh.base.med.dispensingMethod
     */
    @Schema(description = "发药方式;rbmh.base.med.dispensingMethod")
    @Size(max = 100, message = "发药方式;rbmh.base.med.dispensingMethod长度不能超过100个字符")
    @TableField("sd_dps")
    private String sdDps;

    /**
     * 发药单号
     */
    @Schema(description = "发药单号")
    @Size(max = 100, message = "发药单号长度不能超过100个字符")
    @TableField("cd_dps")
    private String cdDps;

    /**
     * 发药单类型;1:购药单, 2: 处方单;phis.stp.dpsType
     */
    @Schema(description = "发药单类型;1:购药单, 2: 处方单;phis.stp.dpsType")
    @Size(max = 100, message = "发药单类型;1:购药单, 2: 处方单;phis.stp.dpsType长度不能超过100个字符")
    @TableField("sd_dps_type")
    private String sdDpsType;

    /**
     * 发药时间
     */
    @Schema(description = "发药时间")
    @TableField("send_time")
    private LocalDateTime sendTime;

    /**
     * 人员唯一标识
     */
    @Schema(description = "人员唯一标识")
    @Size(max = 100, message = "人员唯一标识长度不能超过100个字符")
    @TableField("id_pi")
    private String idPi;

    /**
     * 患者姓名
     */
    @Schema(description = "患者姓名")
    @NotBlank(message = "患者姓名不能为空")
    @Size(max = 30, message = "患者姓名长度不能超过30个字符")
    @TableField("psn_name")
    private String psnName;

    /**
     * 病案号
     */
    @Schema(description = "病案号")
    @Size(max = 100, message = "病案号长度不能超过100个字符")
    @TableField("cd_file")
    private String cdFile;

    /**
     * 是否婴儿
     */
    @Schema(description = "是否婴儿")
    @Size(max = 100, message = "是否婴儿长度不能超过100个字符")
    @TableField("fg_baby")
    private String fgBaby;

    /**
     * 婴儿主键
     */
    @Schema(description = "婴儿主键")
    @Size(max = 100, message = "婴儿主键长度不能超过100个字符")
    @TableField("id_baby")
    private String idBaby;

    /**
     * 床号
     */
    @Schema(description = "床号")
    @Size(max = 100, message = "床号长度不能超过100个字符")
    @TableField("cd_bed")
    private String cdBed;

    /**
     * 开嘱时间
     */
    @Schema(description = "开嘱时间")
    @TableField("dt_order_start")
    private LocalDateTime dtOrderStart;

    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    @TableField("dt_exec")
    private LocalDateTime dtExec;

    /**
     * 医嘱ID
     */
    @Schema(description = "医嘱ID")
    @Size(max = 100, message = "医嘱ID长度不能超过100个字符")
    @TableField("id_ord")
    private String idOrd;

    /**
     * 票据号码
     */
    @Schema(description = "票据号码")
    @Size(max = 100, message = "票据号码长度不能超过100个字符")
    @TableField("sn_bill")
    private String snBill;

    /**
     * 结算主键
     */
    @Schema(description = "结算主键")
    @Size(max = 100, message = "结算主键长度不能超过100个字符")
    @TableField("id_stl")
    private String idStl;

    /**
     * 仓储ID
     */
    @Schema(description = "仓储ID")
    @Size(max = 100, message = "仓储ID长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 发药窗口ID
     */
    @Schema(description = "发药窗口ID")
    @Size(max = 100, message = "发药窗口ID长度不能超过100个字符")
    @TableField("id_sto_win")
    private String idStoWin;

    /**
     * 发药单状态;0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费
     */
    @Schema(description = "发药单状态;0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费")
    @Size(max = 100, message = "发药单状态;0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费长度不能超过100个字符")
    @TableField("fg_status")
    private String fgStatus;

    /**
     * 打印标识
     */
    @Schema(description = "打印标识")
    @Size(max = 100, message = "打印标识长度不能超过100个字符")
    @TableField("fg_print")
    private String fgPrint;

    /**
     * 开单医生
     */
    @Schema(description = "开单医生")
    @Size(max = 100, message = "开单医生长度不能超过100个字符")
    @TableField("id_doc")
    private String idDoc;

    /**
     * 开单科室
     */
    @Schema(description = "开单科室")
    @Size(max = 100, message = "开单科室长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 发药人
     */
    @Schema(description = "发药人")
    @NotBlank(message = "发药人不能为空")
    @Size(max = 30, message = "发药人长度不能超过30个字符")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    /**
     * 核对人
     */
    @Schema(description = "核对人")
    @Size(max = 100, message = "核对人长度不能超过100个字符")
    @TableField("user_check")
    private String userCheck;

    /**
     * 退药单标记;0:发药, 1: 退药
     */
    @Schema(description = "退药单标记;0:发药, 1: 退药")
    @Size(max = 100, message = "退药单标记;0:发药, 1: 退药长度不能超过100个字符")
    @TableField("fg_dps")
    private String fgDps;

    /**
     * 组号
     */
    @Schema(description = "组号")
    @Size(max = 100, message = "组号长度不能超过100个字符")
    @TableField("group_no")
    private String groupNo;

    /**
     * 活动标志
     */
    @Schema(description = "活动标志")
    @Size(max = 100, message = "活动标志长度不能超过100个字符")
    @TableField("fg_active")
    private String fgActive;

    /**
     * 发药时的进货总价
     */
    @Schema(description = "发药时的进货总价")
    @TableField("amt_total_pur")
    private BigDecimal amtTotalPur;

    /**
     * 机构标识
     */
    @Schema(description = "机构标识")
    @Size(max = 100, message = "机构标识长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
