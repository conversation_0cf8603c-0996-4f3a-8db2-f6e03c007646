package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 3506结算记录报文表实体类
 * 
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("nhsa_3506")
@Schema(description = "3506结算记录报文表")
public class Nhsa3506 implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("medical_code")
    private String medicalCode;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("medical_name")
    private String medicalName;

    /**
     * 医疗目录编码
     */
    @Schema(description = "医疗目录编码")
    @Size(max = 100, message = "医疗目录编码长度不能超过100个字符")
    @TableField("med_list_codg")
    private String medListCodg;

    /**
     * 定点医药机构目录编号
     */
    @Schema(description = "定点医药机构目录编号")
    @Size(max = 100, message = "定点医药机构目录编号长度不能超过100个字符")
    @TableField("fixmedins_hilist_id")
    private String fixmedinsHilistId;

    /**
     * 定点医药机构目录名称
     */
    @Schema(description = "定点医药机构目录名称")
    @NotBlank(message = "定点医药机构目录名称不能为空")
    @Size(max = 30, message = "定点医药机构目录名称长度不能超过30个字符")
    @TableField("fixmedins_hilist_name")
    private String fixmedinsHilistName;

    /**
     * 定点医药机构批次流水号
     */
    @Schema(description = "定点医药机构批次流水号")
    @Size(max = 100, message = "定点医药机构批次流水号长度不能超过100个字符")
    @TableField("fixmedins_bchno")
    private String fixmedinsBchno;

    /**
     * 结算ID
     */
    @Schema(description = "结算ID")
    @Size(max = 100, message = "结算ID长度不能超过100个字符")
    @TableField("setl_id")
    private String setlId;

    /**
     * 人员编号
     */
    @Schema(description = "人员编号")
    @Size(max = 100, message = "人员编号长度不能超过100个字符")
    @TableField("psn_no")
    private String psnNo;

    /**
     * 人员证件类型
     */
    @Schema(description = "人员证件类型")
    @Size(max = 100, message = "人员证件类型长度不能超过100个字符")
    @TableField("psn_cert_type")
    private String psnCertType;

    /**
     * 证件号码
     */
    @Schema(description = "证件号码")
    @Size(max = 100, message = "证件号码长度不能超过100个字符")
    @TableField("certno")
    private String certno;

    /**
     * 人员姓名
     */
    @Schema(description = "人员姓名")
    @NotBlank(message = "人员姓名不能为空")
    @Size(max = 30, message = "人员姓名长度不能超过30个字符")
    @TableField("psn_name")
    private String psnName;

    /**
     * 生产批号
     */
    @Schema(description = "生产批号")
    @Size(max = 100, message = "生产批号长度不能超过100个字符")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @TableField("manu_date")
    private LocalDate manuDate;

    /**
     * 有效期止
     */
    @Schema(description = "有效期止")
    @TableField("expy_end")
    private LocalDate expyEnd;

    /**
     * 处方药标志
     */
    @Schema(description = "处方药标志")
    @TableField("rx_flag")
    private String rxFlag;

    /**
     * 拆零标志
     */
    @Schema(description = "拆零标志")
    @TableField("trdn_flag")
    private String trdnFlag;

    /**
     * 最终成交单价
     */
    @Schema(description = "最终成交单价")
    @TableField("finl_trns_pric")
    private BigDecimal finlTrnsPric;

    /**
     * 销售/退货数量
     */
    @Schema(description = "销售/退货数量")
    @TableField("sel_retn_cnt")
    private BigDecimal selRetnCnt;

    /**
     * 销售/退货时间
     */
    @Schema(description = "销售/退货时间")
    @TableField("sel_retn_time")
    private LocalDateTime selRetnTime;

    /**
     * 销售/退货经办人姓名
     */
    @Schema(description = "销售/退货经办人姓名")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField("memo")
    private String memo;

    /**
     * 商品销售流水号
     */
    @Schema(description = "商品销售流水号")
    @TableField("medins_prod_sel_no")
    private String medinsProdSelNo;

    /**
     * 就诊流水号
     */
    @Schema(description = "就诊流水号")
    @TableField("mdtrt_sn")
    private String mdtrtSn;

    /**
     * 药品追溯码信息
     */
    @Schema(description = "药品追溯码信息")
    @TableField("drugtracinfo")
    private String drugtracinfo;

    /**
     * 第三方业务主键
     */
    @Schema(description = "第三方业务主键")
    @TableField("third_id")
    private String thirdId;

    /**
     * 异常内容
     */
    @Schema(description = "异常内容")
    @Size(max = 100, message = "异常内容长度不能超过100个字符")
    @TableField("exp_content")
    private String expContent;

    /**
     * HIS药品ID
     */
    @Schema(description = "HIS药品ID")
    @Size(max = 100, message = "HIS药品ID长度不能超过100个字符")
    @TableField("his_drug_id")
    private String hisDrugId;

    /**
     * HIS企业编码
     */
    @Schema(description = "HIS企业编码")
    @Size(max = 100, message = "HIS企业编码长度不能超过100个字符")
    @TableField("his_entp_code")
    private String hisEntpCode;

    /**
     * HIS企业名称
     */
    @Schema(description = "HIS企业名称")
    @NotBlank(message = "HIS企业名称不能为空")
    @Size(max = 30, message = "HIS企业名称长度不能超过30个字符")
    @TableField("his_entp_name")
    private String hisEntpName;

    /**
     * 请求数据
     */
    @Schema(description = "请求数据")
    @Size(max = 100, message = "请求数据长度不能超过100个字符")
    @TableField("request_data")
    private String requestData;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    @Size(max = 100, message = "响应数据长度不能超过100个字符")
    @TableField("response_data")
    private String responseData;

    /**
     * 两定接口同步状态：0未同步，1已同步
     */
    @Schema(description = "两定接口同步状态：0未同步，1已同步")
    @Size(max = 100, message = "两定接口同步状态：0未同步，1已同步长度不能超过100个字符")
    @TableField("hsa_sync_status")
    private String hsaSyncStatus;

    /**
     * 两定接口同步状态时间
     */
    @Schema(description = "两定接口同步状态时间")
    @TableField("hsa_sync_time")
    private LocalDateTime hsaSyncTime;

    /**
     * 两定接口同步备注
     */
    @Schema(description = "两定接口同步备注")
    @Size(max = 100, message = "两定接口同步备注长度不能超过100个字符")
    @TableField("hsa_sync_remark")
    private String hsaSyncRemark;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表正常,1代表删除）
     */
    @Schema(description = "删除标志（0代表正常,1代表删除）")
    @Size(max = 100, message = "删除标志（0代表正常,1代表删除）长度不能超过100个字符")
    @TableField("delete_flag")
    private String deleteFlag;
    /**
     * 云速付用户ID
     */
    @Schema(description = "云速付用户ID")
    @TableField("ymf_user_id")
    private Long ymfUserId;

    /**
     * 云速付用户名
     */
    @Schema(description = "云速付用户名")
    @TableField("ymf_user_name")
    private String ymfUserName;

    /**
     * 云速付昵称
     */
    @Schema(description = "云速付昵称")
    @TableField("ymf_nick_name")
    private String ymfNickName;

        /**
     * 处方序号
     */
    @Schema(description = "处方序号")
    @TableField("cfxh")
    private String cfxh;

    /**
     * 处方明细序号
     */
    @Schema(description = "处方明细序号")
    @TableField("cfmxxh")
    private String cfmxxh;

} 
