package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 仓储物品出入库记录实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_flow")
@Schema(description = "仓储物品出入库记录")
public class YsfStoFlow implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 出入库主键
     */
    @Schema(description = "出入库主键")
    @TableId(value = "id_sto_flow", type = IdType.AUTO)
    private Long idStoFlow;

    /**
     * 入出类型;1: 入库, 2: 出库, 3:养护
     */
    @Schema(description = "入出类型;1: 入库, 2: 出库, 3:养护")
    @Size(max = 100, message = "入出类型;1: 入库, 2: 出库, 3:养护长度不能超过100个字符")
    @TableField("sd_flow")
    private String sdFlow;

    /**
     * 入出子类型;11: 采购,12: 申领,18:盘盈,19:其它入库,21.采购退库,22.调拨,28:盘亏,29:其它出库,31:破损
     */
    @Schema(description = "入出子类型;11: 采购,12: 申领,18:盘盈,19:其它入库,21.采购退库,22.调拨,28:盘亏,29:其它出库,31:破损")
    @Size(max = 100, message = "入出子类型;11: 采购,12: 申领,18:盘盈,19:其它入库,21.采购退库,22.调拨,28:盘亏,29:其它出库,31:破损长度不能超过100个字符")
    @TableField("sd_flow_sub")
    private String sdFlowSub;

    /**
     * 出入库单号
     */
    @Schema(description = "出入库单号")
    @Size(max = 100, message = "出入库单号长度不能超过100个字符")
    @TableField("cd_sto_flow")
    private String cdStoFlow;

    /**
     * 入出库业务时间;如采购日期等
     */
    @Schema(description = "入出库业务时间;如采购日期等")
    @TableField("dt_flow")
    private LocalDateTime dtFlow;

    /**
     * 目标机构
     */
    @Schema(description = "目标机构")
    @Size(max = 100, message = "目标机构长度不能超过100个字符")
    @TableField("id_org_target")
    private String idOrgTarget;

    /**
     * 目标部门;采购时填写供应商,调拨时填写仓储或者科室
     */
    @Schema(description = "目标部门;采购时填写供应商,调拨时填写仓储或者科室")
    @Size(max = 100, message = "目标部门;采购时填写供应商,调拨时填写仓储或者科室长度不能超过100个字符")
    @TableField("id_dept_target")
    private String idDeptTarget;

    /**
     * 目标名称
     */
    @Schema(description = "目标名称")
    @Size(max = 100, message = "目标名称长度不能超过100个字符")
    @TableField("na_dept_target")
    private String naDeptTarget;

    /**
     * 流转状态;0:新建, 9:已确认,1:提交,2:接收方确认,3:提交方确认,99:已作废
     */
    @Schema(description = "流转状态;0:新建, 9:已确认,1:提交,2:接收方确认,3:提交方确认,99:已作废")
    @Size(max = 100, message = "流转状态;0:新建, 9:已确认,1:提交,2:接收方确认,3:提交方确认,99:已作废长度不能超过100个字符")
    @TableField("sd_flow_status")
    private String sdFlowStatus;

    /**
     * 有效标识;sys.sd.yesOrNo
     */
    @Schema(description = "有效标识;sys.sd.yesOrNo")
    @Size(max = 100, message = "有效标识;sys.sd.yesOrNo长度不能超过100个字符")
    @TableField("fg_active")
    private String fgActive;

    /**
     * 购入方式;1.货到票到,2货到票未到,3.票到货未到
     */
    @Schema(description = "购入方式;1.货到票到,2货到票未到,3.票到货未到")
    @Size(max = 100, message = "购入方式;1.货到票到,2货到票未到,3.票到货未到长度不能超过100个字符")
    @TableField("purchase_way")
    private String purchaseWay;

    /**
     * 验收标志;sys.sd.yesOrNo,0未验收,1已验收. 默认0
     */
    @Schema(description = "验收标志;sys.sd.yesOrNo,0未验收,1已验收. 默认0")
    @Size(max = 100, message = "验收标志;sys.sd.yesOrNo,0未验收,1已验收. 默认0长度不能超过100个字符")
    @TableField("fg_accept")
    private String fgAccept;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 100, message = "备注长度不能超过100个字符")
    @TableField("memo")
    private String memo;

    /**
     * 扩展字段;json格式扩展字段
     */
    @Schema(description = "扩展字段;json格式扩展字段")
    @Size(max = 100, message = "扩展字段;json格式扩展字段长度不能超过100个字符")
    @TableField("json_field")
    private String jsonField;

    /**
     * 仓储ID
     */
    @Schema(description = "仓储ID")
    @Size(max = 100, message = "仓储ID长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 单据确认人
     */
    @Schema(description = "单据确认人")
    @Size(max = 100, message = "单据确认人长度不能超过100个字符")
    @TableField("confirm_user")
    private String confirmUser;

    /**
     * 单据确认人姓名
     */
    @Schema(description = "单据确认人姓名")
    @Size(max = 100, message = "单据确认人姓名长度不能超过100个字符")
    @TableField("na_confirm_user")
    private String naConfirmUser;

    /**
     * 单据确时间
     */
    @Schema(description = "单据确时间")
    @TableField("confirm_time")
    private LocalDateTime confirmTime;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
