package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 住院发药单明细实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@TableName("ysf_sto_dps_sub_hos")
@Schema(description = "住院发药单明细")
public class YsfStoDpsSubHos implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发药单明细ID
     */
    @Schema(description = "发药单明细ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 处方明细序号
     */
    @Schema(description = "处方明细序号")
    @Size(max = 32, message = "处方明细序号长度不能超过32个字符")
    @TableField("cfmxxh")
    private String cfmxxh;

    /**
     * 原发药单处方明细序号;用于退药单
     */
    @Schema(description = "原发药单处方明细序号;用于退药单")
    @Size(max = 32, message = "原发药单处方明细序号长度不能超过32个字符")
    @TableField("ori_cfmxxh")
    private String oriCfmxxh;

    /**
     * 原发药单明细ID;用于退药单，原发药单明细id
     */
    @Schema(description = "原发药单明细ID;用于退药单，原发药单明细id")
    @Size(max = 24, message = "原发药单明细ID长度不能超过24个字符")
    @TableField("ori_id")
    private String oriId;

    /**
     * 发药单序号
     */
    @Schema(description = "发药单序号")
    @Size(max = 24, message = "发药单序号长度不能超过24个字符")
    @TableField("cfxh")
    private String cfxh;

    /**
     * 发药单ID
     */
    @Schema(description = "发药单ID")
    @Size(max = 24, message = "发药单ID长度不能超过24个字符")
    @TableField("id_dps")
    private String idDps;

    /**
     * 费用明细ID
     */
    @Schema(description = "费用明细ID")
    @Size(max = 24, message = "费用明细ID长度不能超过24个字符")
    @TableField("id_fee")
    private String idFee;

    /**
     * 费用名称
     */
    @Schema(description = "费用名称")
    @Size(max = 255, message = "费用名称长度不能超过255个字符")
    @TableField("na_fee")
    private String naFee;

    /**
     * 药品编码
     */
    @Schema(description = "药品编码")
    @Size(max = 255, message = "药品编码长度不能超过255个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 追溯码
     */
    @Schema(description = "追溯码")
    @TableField("drugtracinfo")
    private String drugtracinfo;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @TableField("price_sale")
    private BigDecimal priceSale;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @TableField("sel_retn_cnt")
    private Integer selRetnCnt;

    /**
     * 金额
     */
    @Schema(description = "金额")
    @TableField("amt_total")
    private BigDecimal amtTotal;

    /**
     * 实发金额
     */
    @Schema(description = "实发金额")
    @TableField("amt_total_dps")
    private BigDecimal amtTotalDps;

    /**
     * 包装单位
     */
    @Schema(description = "包装单位")
    @Size(max = 32, message = "包装单位长度不能超过32个字符")
    @TableField("unit_sale")
    private String unitSale;

    /**
     * 包装系数
     */
    @Schema(description = "包装系数")
    @TableField("unit_sale_factor")
    private Integer unitSaleFactor;

    /**
     * 发药时的进货总价
     */
    @Schema(description = "发药时的进货总价")
    @TableField("amt_total_pur")
    private BigDecimal amtTotalPur;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 24, message = "机构编号长度不能超过24个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 32, message = "医疗机构编码长度不能超过32个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @Size(max = 32, message = "医疗机构名称长度不能超过32个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 64, message = "创建者长度不能超过64个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 64, message = "更新者长度不能超过64个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableField("del_flag")
    private String delFlag;

    /**
     * 药品数量
     */
    @Schema(description = "药品数量")
    @TableField("quantity")
    private Integer quantity;

    /**
     * 药品单位
     */
    @Schema(description = "药品单位")
    @Size(max = 32, message = "药品单位长度不能超过32个字符")
    @TableField("unit")
    private String unit;

    /**
     * 已采集的追溯码数量
     */
    @Schema(description = "已采集的追溯码数量")
    @TableField("trac_cnt")
    private Integer tracCnt;

    /**
     * 患者唯一住院号
     */
    @Schema(description = "患者唯一住院号")
    @Size(max = 100, message = "患者唯一住院号长度不能超过100个字符")
    @TableField("pat_in_hos_id")
    private String patInHosId;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    @Size(max = 24, message = "患者ID长度不能超过24个字符")
    @TableField("patient_id")
    private String patientId;

    /**
     * 患者名称
     */
    @Schema(description = "患者名称")
    @Size(max = 32, message = "患者名称长度不能超过32个字符")
    @TableField("psn_name")
    private String psnName;
} 