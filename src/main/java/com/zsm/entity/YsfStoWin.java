package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 药房窗口实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_win")
@Schema(description = "药房窗口")
public class YsfStoWin implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 窗口id
     */
    @Schema(description = "窗口id")
    @TableId(value = "id_win", type = IdType.AUTO)
    private Long idWin;

    /**
     * 窗口编号,预留
     */
    @Schema(description = "窗口编号,预留")
    @Size(max = 100, message = "窗口编号,预留长度不能超过100个字符")
    @TableField("cd_win")
    private String cdWin;

    /**
     * 窗口名称
     */
    @Schema(description = "窗口名称")
    @Size(max = 100, message = "窗口名称长度不能超过100个字符")
    @TableField("na_win")
    private String naWin;

    /**
     * 库房id
     */
    @Schema(description = "库房id")
    @Size(max = 100, message = "库房id长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 部门id
     */
    @Schema(description = "部门id")
    @Size(max = 100, message = "部门id长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 有效标识
     */
    @Schema(description = "有效标识")
    @Size(max = 100, message = "有效标识长度不能超过100个字符")
    @TableField("fg_active")
    private String fgActive;

    /**
     * id地址,预留
     */
    @Schema(description = "id地址,预留")
    @Size(max = 100, message = "id地址,预留长度不能超过100个字符")
    @TableField("win_ip")
    private String winIp;

    /**
     * mac地址,预留
     */
    @Schema(description = "mac地址,预留")
    @Size(max = 100, message = "mac地址,预留长度不能超过100个字符")
    @TableField("win_mac")
    private String winMac;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
