package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仓储盘点明细实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_check_sub")
@Schema(description = "仓储盘点明细")
public class YsfStoCheckSub implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 盘点明细主键
     */
    @Schema(description = "盘点明细主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 盘点主键
     */
    @Schema(description = "盘点主键")
    @Size(max = 100, message = "盘点主键长度不能超过100个字符")
    @TableField("id_sto_check")
    private String idStoCheck;

    /**
     * 药品主键
     */
    @Schema(description = "药品主键")
    @Size(max = 100, message = "药品主键长度不能超过100个字符")
    @TableField("id_med_pro")
    private String idMedPro;

    /**
     * 库存主键;为空表示新增数据
     */
    @Schema(description = "库存主键;为空表示新增数据")
    @Size(max = 100, message = "库存主键;为空表示新增数据长度不能超过100个字符")
    @TableField("id_sto_inv")
    private String idStoInv;

    /**
     * 药品批号
     */
    @Schema(description = "药品批号")
    @Size(max = 100, message = "药品批号长度不能超过100个字符")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 药品效期
     */
    @Schema(description = "药品效期")
    @TableField("expy_end")
    private LocalDateTime expyEnd;

    /**
     * 零售价格
     */
    @Schema(description = "零售价格")
    @TableField("price_sale")
    private BigDecimal priceSale;

    /**
     * 进货价格;新增库存时必填,自动新增时从库存查询
     */
    @Schema(description = "进货价格;新增库存时必填,自动新增时从库存查询")
    @TableField("price_pur")
    private BigDecimal pricePur;

    /**
     * 盘前数量
     */
    @Schema(description = "盘前数量")
    @TableField("amt_check_bgn")
    private Integer amtCheckBgn;

    /**
     * 实盘数量
     */
    @Schema(description = "实盘数量")
    @TableField("amt_check_end")
    private Integer amtCheckEnd;

    /**
     * 变动数量
     */
    @Schema(description = "变动数量")
    @TableField("amt_change")
    private Integer amtChange;

    /**
     * 养护原因;1.次品,2.伪劣,3.破损,4.霉变
     */
    @Schema(description = "养护原因;1.次品,2.伪劣,3.破损,4.霉变")
    @Size(max = 100, message = "养护原因;1.次品,2.伪劣,3.破损,4.霉变长度不能超过100个字符")
    @TableField("sd_reason")
    private String sdReason;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
