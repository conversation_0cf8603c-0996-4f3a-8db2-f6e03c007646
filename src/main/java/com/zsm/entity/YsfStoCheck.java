package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 仓储盘点实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_check")
@Schema(description = "仓储盘点")
public class YsfStoCheck implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 盘点主键
     */
    @Schema(description = "盘点主键")
    @TableId(value = "id_sto_check", type = IdType.AUTO)
    private Long idStoCheck;

    /**
     * 仓储主键
     */
    @Schema(description = "仓储主键")
    @Size(max = 100, message = "仓储主键长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 盘点单号
     */
    @Schema(description = "盘点单号")
    @Size(max = 100, message = "盘点单号长度不能超过100个字符")
    @TableField("cd_sto_check")
    private String cdStoCheck;

    /**
     * 盘点开始时间
     */
    @Schema(description = "盘点开始时间")
    @TableField("dt_check_begin")
    private LocalDateTime dtCheckBegin;

    /**
     * 盘点结束时间
     */
    @Schema(description = "盘点结束时间")
    @TableField("dt_check_end")
    private LocalDateTime dtCheckEnd;

    /**
     * 盘点状态;0: 盘点中, 1:完成盘点, 9: 盘点作废
     */
    @Schema(description = "盘点状态;0: 盘点中, 1:完成盘点, 9: 盘点作废")
    @Size(max = 100, message = "盘点状态;0: 盘点中, 1:完成盘点, 9: 盘点作废长度不能超过100个字符")
    @TableField("fg_sto_check")
    private String fgStoCheck;

    /**
     * 盈亏状态;-1: 盘亏,0:正常,1:盘盈
     */
    @Schema(description = "盈亏状态;-1: 盘亏,0:正常,1:盘盈")
    @Size(max = 100, message = "盈亏状态;-1: 盘亏,0:正常,1:盘盈长度不能超过100个字符")
    @TableField("sd_pol")
    private String sdPol;

    /**
     * 盘点类型;1:库存,2:养护
     */
    @Schema(description = "盘点类型;1:库存,2:养护")
    @Size(max = 100, message = "盘点类型;1:库存,2:养护长度不能超过100个字符")
    @TableField("sd_check")
    private String sdCheck;

    /**
     * 盘点信息;盘点摘要信息
     */
    @Schema(description = "盘点信息;盘点摘要信息")
    @Size(max = 100, message = "盘点信息;盘点摘要信息长度不能超过100个字符")
    @TableField("des_sto_check")
    private String desStoCheck;

    /**
     * 作废原因
     */
    @Schema(description = "作废原因")
    @Size(max = 100, message = "作废原因长度不能超过100个字符")
    @TableField("des_invalid")
    private String desInvalid;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
