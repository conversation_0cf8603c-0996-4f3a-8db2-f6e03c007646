package com.zsm.config;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import javax.sql.DataSource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;

/**
 * 数据库初始化器
 * 在应用启动时检查数据库表是否存在，如果不存在则自动创建
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Value("${app.database.auto-init:false}")
    private boolean autoInit;

    @Resource
    private DataSource dataSource;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        if (!autoInit) {
            log.info("数据库自动初始化已禁用，跳过检查");
            return;
        }
        
        log.info("开始检查数据库表...");
        
        // 检查表是否存在
        if (!isTableExists("nhsa_3505")) {
            log.info("检测到数据库表不存在，开始创建表...");
            initializeDatabase();
            log.info("数据库表创建完成！");
        } else {
            log.info("数据库表已存在，跳过初始化");
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean isTableExists(String tableName) {
        try (Connection connection = dataSource.getConnection()) {
            // 获取当前数据库名
            String catalog = connection.getCatalog();
            log.info("当前数据库: {}", catalog);
            
            // 查询表是否存在
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                    "WHERE table_schema = ? AND table_name = ?";
            
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, catalog, tableName);
            boolean exists = count != null && count > 0;
            
            log.info("表 {} 存在状态: {}", tableName, exists);
            return exists;
            
        } catch (Exception e) {
            log.error("检查表是否存在时发生错误", e);
            return false;
        }
    }

    /**
     * 初始化数据库
     */
    private void initializeDatabase() {
        try {
            // 读取SQL脚本文件
            ClassPathResource resource = new ClassPathResource("sql/schema.sql");
            byte[] bdata = FileCopyUtils.copyToByteArray(resource.getInputStream());
            String sql = new String(bdata, StandardCharsets.UTF_8);

            // 使用更智能的SQL语句分割方法
            String[] sqlStatements = splitSqlStatements(sql);

            // 执行每个SQL语句
            for (String statement : sqlStatements) {
                statement = statement.trim();
                if (!statement.isEmpty() && !statement.startsWith("--")) {
                    try {
                        log.info("执行SQL: {}", statement);
                        jdbcTemplate.execute(statement);
                    } catch (Exception e) {
                        // 某些语句可能会失败（如CREATE DATABASE），但可以继续
                        log.warn("执行SQL语句失败，但继续执行: {} - 错误: {}", statement, e.getMessage());
                    }
                }
            }

        } catch (IOException e) {
            log.error("读取SQL脚本文件失败", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }

    /**
     * 智能分割SQL语句，正确处理字符串中的分号
     */
    private String[] splitSqlStatements(String sql) {
        java.util.List<String> statements = new java.util.ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();
        boolean inString = false;
        boolean inComment = false;
        char stringDelimiter = 0;
        
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            char nextChar = (i + 1 < sql.length()) ? sql.charAt(i + 1) : 0;
            
            // 处理行注释
            if (!inString && !inComment && c == '-' && nextChar == '-') {
                inComment = true;
                currentStatement.append(c);
                continue;
            }
            
            // 处理换行符（结束行注释）
            if (inComment && (c == '\n' || c == '\r')) {
                inComment = false;
                currentStatement.append(c);
                continue;
            }
            
            // 如果在注释中，直接添加字符
            if (inComment) {
                currentStatement.append(c);
                continue;
            }
            
            // 处理字符串开始
            if (!inString && (c == '\'' || c == '"' || c == '`')) {
                inString = true;
                stringDelimiter = c;
                currentStatement.append(c);
                continue;
            }
            
            // 处理字符串结束
            if (inString && c == stringDelimiter) {
                // 检查是否是转义的引号
                if (i > 0 && sql.charAt(i - 1) != '\\') {
                    inString = false;
                    stringDelimiter = 0;
                }
                currentStatement.append(c);
                continue;
            }
            
            // 处理分号（只有在字符串外才分割）
            if (!inString && !inComment && c == ';') {
                String statement = currentStatement.toString().trim();
                if (!statement.isEmpty()) {
                    statements.add(statement);
                }
                currentStatement = new StringBuilder();
                continue;
            }
            
            currentStatement.append(c);
        }
        
        // 添加最后一个语句
        String lastStatement = currentStatement.toString().trim();
        if (!lastStatement.isEmpty()) {
            statements.add(lastStatement);
        }
        
        return statements.toArray(new String[0]);
    }
} 