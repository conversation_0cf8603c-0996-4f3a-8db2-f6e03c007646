package com.zsm.config;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.util.Collections;

/**
 * MyBatis Plus 代码生成器配置类
 * 用于根据数据库表结构自动生成实体类、Mapper、Service、Controller等代码
 *
 * <AUTHOR>
 * @since 2024
 */
public class CodeGeneratorConfig {

    private static final String dbUrl = "*******************************************************************************************************************************************************";

    private static final String dbUsername = "fuyangrenyi";

    private static final String dbPassword = "hPeBF8EXB7PzaaGX";

    private static final String dbDriver = "com.mysql.cj.jdbc.Driver";

    private static final String packageName = "com.zsm";

    private static final String author = "Generator";


    public static void main(String[] args) {
        // 直接调用生成方法
        CodeGeneratorConfig generator = new CodeGeneratorConfig();
        // 配置参数
        String tableNames = ""; // 要生成的表名，用逗号分隔。留空则生成所有表

        generator.generateCode(tableNames, author);
    }

    /**
     * 执行代码生成
     *
     * @param tableNames 要生成的表名，多个表名用逗号分隔，如果为空则生成所有表
     * @param author     作者名称
     */
    public void generateCode(String tableNames, String author) {

        // 如果作者为空，使用默认作者
        final String finalAuthor = (author == null || author.trim()
                .isEmpty()) ? "Generated" : author;

        String projectPath = System.getProperty("user.dir");
        String outputDir = projectPath + "/src/main/java";
        String xmlOutputDir = projectPath + "/src/main/resources/mapper";

        FastAutoGenerator.create(dbUrl, dbUsername, dbPassword)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author(finalAuthor) // 设置作者
                            .disableOpenDir() // 禁止打开输出目录
                            .dateType(DateType.TIME_PACK) // 使用java.time包下的时间类型
                            .outputDir(outputDir); // 指定输出目录
                })
                // 包配置
                .packageConfig(builder -> {
                    builder.parent(packageName) // 设置父包名
                            .entity("entity") // 设置实体类包名
                            .service("service") // 设置 Service 包名
                            .serviceImpl("service.impl") // 设置 Service Impl 包名
                            .mapper("mapper") // 设置 Mapper 包名
                            .controller("controller") // 设置 Controller 包名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, xmlOutputDir)); // 设置mapperXml生成路径
                })
                // 策略配置
                .strategyConfig(builder -> {
                    if (tableNames != null && !tableNames.trim()
                            .isEmpty()) {
                        builder.addInclude(getTableNames(tableNames)); // 设置需要生成的表名
                    }

                    // 实体类策略配置
                    builder.entityBuilder()
                            .enableLombok() // 开启 lombok 模型
                            .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                            .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                            .idType(IdType.AUTO) // 主键类型设置为AUTO
                            .enableFileOverride(); // 允许覆盖已存在的文件

                    // Controller策略配置
                    builder.controllerBuilder()
                            .enableRestStyle() // 开启生成@RestController 控制器
                            .enableHyphenStyle() // 开启驼峰转连字符
                            .enableFileOverride(); // 允许覆盖已存在的文件

                    // Service策略配置
                    builder.serviceBuilder()
                            .formatServiceFileName("%sService") // 格式化 service 接口文件名称
                            .formatServiceImplFileName("%sServiceImpl") // 格式化 service 实现类文件名称
                            .enableFileOverride(); // 允许覆盖已存在的文件

                    // Mapper策略配置
                    builder.mapperBuilder()
                            .enableMapperAnnotation() // 开启 @Mapper 注解
                            .formatMapperFileName("%sMapper") // 格式化 mapper 文件名称
                            .formatXmlFileName("%sMapper") // 格式化 xml 实现类文件名称
                            .enableFileOverride(); // 允许覆盖已存在的文件
                })
                // 模板引擎配置，使用Velocity模板引擎
                .templateEngine(new VelocityTemplateEngine())
                // 执行
                .execute();

        System.out.println("代码生成完成！");
        System.out.println("生成路径：" + outputDir);
        System.out.println("XML文件路径：" + xmlOutputDir);
        System.out.println("包路径：" + packageName);
        System.out.println("已使用OpenAPI 3.0注解，实体类为独立类型（不继承BaseEntity）");

        // 后处理生成的实体类
        System.out.println("正在处理生成的实体类...");
        String entityDirPath = outputDir + "/" + packageName.replace(".", "/") + "/entity";
        EntityPostProcessor.processEntityDirectory(entityDirPath);
        System.out.println("实体类处理完成！");
    }

    /**
     * 处理表名字符串，转换为字符串数组
     *
     * @param tableNames 表名字符串，多个表名用逗号分隔
     * @return 表名数组
     */
    private String[] getTableNames(String tableNames) {
        if (tableNames == null || tableNames.trim()
                .isEmpty()) {
            return new String[]{}; // 返回空数组表示生成所有表
        }
        return tableNames.split(",");
    }

    /**
     * 快速生成单表代码
     *
     * @param tableName 表名
     * @param author    作者
     */
    public void generateSingleTable(String tableName, String author) {
        generateCode(tableName, author);
    }

    /**
     * 快速生成所有表代码
     *
     * @param author 作者
     */
    public void generateAllTables(String author) {
        generateCode("", author);
    }
} 