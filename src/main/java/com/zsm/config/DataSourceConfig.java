package com.zsm.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据源配置
 * <p>
 * 提供主数据源Bean，确保MyBatis能找到数据源
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class DataSourceConfig {

    /**
     * 主数据源配置
     */
    @Bean(name = "dataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.master")
    public DataSource dataSource() {
        return new DruidDataSource();
    }
}