package com.zsm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zsm.entity.YsfStoTcStatus;
import com.zsm.model.dto.YsfStoTcStatusQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 追溯码状态记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Mapper
public interface YsfStoTcStatusMapper extends BaseMapper<YsfStoTcStatus> {
    /**
     * 根据条件查询追溯码状态记录分页列表
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 状态记录分页列表
     */
    IPage<YsfStoTcStatus> selectStatusRecordList(IPage<YsfStoTcStatus> page, @Param("queryDto") YsfStoTcStatusQueryDto queryDto);
}
