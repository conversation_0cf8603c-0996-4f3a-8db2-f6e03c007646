package com.zsm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsm.entity.Nhsa3505CntCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售数据3505采集数量 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper
public interface Nhsa3505CntCountMapper extends BaseMapper<Nhsa3505CntCount> {
/**
     * 查询销售数据3505采集数量
     *
     * @param id 销售数据3505采集数量主键
     * @return 销售数据3505采集数量
     */
    public Nhsa3505CntCount selectNhsa3505CntCountById(Long id);

    /**
     * 查询销售数据3505采集数量列表
     *
     * @param nhsa3505CntCount 销售数据3505采集数量
     * @return 销售数据3505采集数量集合
     */
    public List<Nhsa3505CntCount> selectNhsa3505CntCountList(Nhsa3505CntCount nhsa3505CntCount);

    /**
     * 新增销售数据3505采集数量
     *
     * @param nhsa3505CntCount 销售数据3505采集数量
     * @return 结果
     */
    public int insertNhsa3505CntCount(Nhsa3505CntCount nhsa3505CntCount);

    /**
     * 修改销售数据3505采集数量
     *
     * @param nhsa3505CntCount 销售数据3505采集数量
     * @return 结果
     */
    public int updateNhsa3505CntCount(Nhsa3505CntCount nhsa3505CntCount);

    /**
     * 删除销售数据3505采集数量
     *
     * @param id 销售数据3505采集数量主键
     * @return 结果
     */
    public int deleteNhsa3505CntCountById(Long id);

    /**
     * 批量删除销售数据3505采集数量
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNhsa3505CntCountByIds(Long[] ids);

    /**
     * 根据日期范围查询销售数据3505采集数量列表
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售数据3505采集数量集合
     */
    public List<Nhsa3505CntCount> selectNhsa3505CntCountListByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
