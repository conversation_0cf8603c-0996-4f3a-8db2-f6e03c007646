package com.zsm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsm.entity.YsfStoDpsSub;
import com.zsm.model.dto.DispensingRecordDetailQueryDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 发药单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Mapper
public interface YsfStoDpsSubMapper extends BaseMapper<YsfStoDpsSub> {
    /**
     * 获取今天已发药的发药单明细列表
     *
     * @return 发药单明细列表
     */
    List<YsfStoDpsSub> getSendStatusList();

    /**
     * 根据条件查询发药单明细列表
     *
     * @param queryDto 查询参数
     * @return 发药单明细列表
     */
    List<YsfStoDpsSub> queryDispensingRecordDetailsWithConditions(DispensingRecordDetailQueryDto queryDto);
}
