package com.zsm.utils;

import com.zsm.common.exception.BusinessException;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.service.SignNoCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 医保接口重试工具类
 * 提供统一的token过期重试机制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class NhsaRetryUtil {

    @Autowired
    private SignNoCacheService signNoCacheService;

    /**
     * Token过期错误标识
     */
    private static final String TOKEN_EXPIRED_MSG = "FSI-调用签到信息查询失败";

    /**
     * 执行带重试的医保接口调用（需要signNo参数）
     * 
     * @param nhsaAccount 医保账户信息
     * @param apiCall 接口调用函数，参数为signNo
     * @param <T> 返回类型
     * @return 接口调用结果
     */
    public <T> T executeWithRetry(NhsaAccount nhsaAccount, Function<String, T> apiCall) {
        // 首次获取签到令牌
        String signNo = signNoCacheService.getSignNo(nhsaAccount);
        
        try {
            // 第一次尝试调用
            return apiCall.apply(signNo);
        } catch (BusinessException e) {
            // 检查是否是token过期错误
            if (isTokenExpiredError(e)) {
                log.warn("检测到签到令牌过期，尝试重新获取签到令牌并重试...");
                
                // 清除缓存的签到令牌
                signNoCacheService.clearSignNoCache(nhsaAccount);
                
                // 重新获取签到令牌
                signNo = signNoCacheService.getSignNo(nhsaAccount);
                log.info("重新获取到新的签名号: {}", signNo);
                
                // 使用新的签到令牌重试
                T result = apiCall.apply(signNo);
                log.info("使用新的签到令牌重试成功");
                return result;
            } else {
                // 如果不是token过期错误，继续抛出异常
                throw e;
            }
        }
    }

    /**
     * 执行带重试的医保接口调用（自动获取signNo）
     * 
     * @param nhsaAccount 医保账户信息
     * @param apiCall 接口调用函数，无参数
     * @param <T> 返回类型
     * @return 接口调用结果
     */
    public <T> T executeWithRetry(NhsaAccount nhsaAccount, Supplier<T> apiCall) {
        try {
            // 第一次尝试调用
            return apiCall.get();
        } catch (BusinessException e) {
            // 检查是否是token过期错误
            if (isTokenExpiredError(e)) {
                log.warn("检测到签到令牌过期，尝试重新获取签到令牌并重试...");
                
                // 清除缓存的签到令牌
                signNoCacheService.clearSignNoCache(nhsaAccount);
                
                // 重新获取签到令牌
                String newSignNo = signNoCacheService.getSignNo(nhsaAccount);
                log.info("重新获取到新的签名号: {}", newSignNo);
                
                // 使用新的签到令牌重试
                T result = apiCall.get();
                log.info("使用新的签到令牌重试成功");
                return result;
            } else {
                // 如果不是token过期错误，继续抛出异常
                throw e;
            }
        }
    }

    /**
     * 检查是否是token过期错误
     * 
     * @param e 业务异常
     * @return true 如果是token过期错误
     */
    private boolean isTokenExpiredError(BusinessException e) {
        return e.getMessage() != null && e.getMessage().contains(TOKEN_EXPIRED_MSG);
    }

    /**
     * 执行带重试的医保接口调用（静态方法版本，用于工具类调用）
     * 
     * @param nhsaAccount 医保账户信息
     * @param apiCall 接口调用函数，参数为signNo
     * @param <T> 返回类型
     * @return 接口调用结果
     */
    public static <T> T executeWithRetryStatic(NhsaAccount nhsaAccount, Function<String, T> apiCall) {
        // 首次获取签到令牌
        String signNo = NhsaHttpUtil.getSignNo(nhsaAccount);
        
        try {
            // 第一次尝试调用
            return apiCall.apply(signNo);
        } catch (BusinessException e) {
            // 检查是否是token过期错误
            if (e.getMessage() != null && e.getMessage().contains(TOKEN_EXPIRED_MSG)) {
                log.warn("检测到签到令牌过期，尝试重新获取签到令牌并重试...");
                
                // 重新获取签到令牌
                signNo = NhsaHttpUtil.getSignNo(nhsaAccount);
                log.info("重新获取到新的签名号: {}", signNo);
                
                // 使用新的签到令牌重试
                T result = apiCall.apply(signNo);
                log.info("使用新的签到令牌重试成功");
                return result;
            } else {
                // 如果不是token过期错误，继续抛出异常
                throw e;
            }
        }
    }
}
