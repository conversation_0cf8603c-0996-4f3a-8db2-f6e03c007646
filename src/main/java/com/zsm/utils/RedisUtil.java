package com.zsm.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 * 提供常用的Redis操作方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class RedisUtil {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设置缓存
     * 
     * @param key   键
     * @param value 值
     * @param time  过期时间(秒)
     */
    public void set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                redisTemplate.opsForValue().set(key, value);
            }
        } catch (Exception e) {
            log.error("Redis设置缓存失败, key: {}, value: {}, time: {}", key, value, time, e);
        }
    }

    /**
     * 设置缓存，指定过期时间
     * 
     * @param key      键
     * @param value    值
     * @param duration 过期时间
     */
    public void set(String key, Object value, Duration duration) {
        try {
            redisTemplate.opsForValue().set(key, value, duration);
        } catch (Exception e) {
            log.error("Redis设置缓存失败, key: {}, value: {}, duration: {}", key, value, duration, e);
        }
    }

    /**
     * 获取缓存
     * 
     * @param key 键
     * @param <T> 泛型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        try {
            return key == null ? null : (T) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Redis获取缓存失败, key: {}", key, e);
            return null;
        }
    }

    /**
     * 判断key是否存在
     * 
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("Redis判断key是否存在失败, key: {}", key, e);
            return false;
        }
    }

    /**
     * 删除缓存
     * 
     * @param key 键
     */
    public void delete(String key) {
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Redis删除缓存失败, key: {}", key, e);
        }
    }

    /**
     * 设置缓存到当天24点过期
     * 
     * @param key   键
     * @param value 值
     */
    public void setUntilMidnight(String key, Object value) {
        try {
            // 计算到当天24点的秒数
            LocalDateTime midnight = LocalDate.now().plusDays(1).atStartOfDay();
            Duration duration = Duration.between(LocalDateTime.now(), midnight);
            
            redisTemplate.opsForValue().set(key, value, duration);
            log.debug("Redis设置缓存到当天24点过期, key: {}, 过期时间: {} 秒", key, duration.getSeconds());
        } catch (Exception e) {
            log.error("Redis设置缓存到当天24点过期失败, key: {}, value: {}", key, value, e);
        }
    }

    /**
     * 获取key的剩余过期时间
     * 
     * @param key 键
     * @return 剩余过期时间(秒)，-1表示永不过期，-2表示key不存在
     */
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -2;
        } catch (Exception e) {
            log.error("Redis获取key过期时间失败, key: {}", key, e);
            return -2;
        }
    }

    /**
     * 设置key的过期时间
     * 
     * @param key  键
     * @param time 过期时间(秒)
     * @return true 成功 false 失败
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                return Boolean.TRUE.equals(redisTemplate.expire(key, time, TimeUnit.SECONDS));
            }
            return false;
        } catch (Exception e) {
            log.error("Redis设置key过期时间失败, key: {}, time: {}", key, time, e);
            return false;
        }
    }
}