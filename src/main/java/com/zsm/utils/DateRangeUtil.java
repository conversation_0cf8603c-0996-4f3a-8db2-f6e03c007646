package com.zsm.utils;

import com.zsm.model.domain.DateRange;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * 日期范围工具类
 * 提供各种日期范围计算的静态方法
 *
 * <AUTHOR>
 * @date 2024
 */
public class DateRangeUtil {

    /**
     * 日期格式化器：yyyy-MM-dd
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 计算本周的日期范围（周日到周六）
     * 如果今天是周日则从今天开始，否则从上周日开始
     * 
     * @return 本周日期范围（周日到周六）
     */
    public static DateRange calculateCurrentWeekRange() {
        LocalDate today = LocalDate.now();
        
        // 计算本周日（如果今天是周日则为今天，否则为上周日）
        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
        
        // 计算本周六
        LocalDate endOfWeek = startOfWeek.plusDays(6);
        
        return DateRange.builder()
                .startDate(startOfWeek.format(DATE_FORMATTER))
                .endDate(endOfWeek.format(DATE_FORMATTER))
                .build();
    }

    /**
     * 计算上周的日期范围（周日到周六）
     * 
     * @return 上周日期范围（周日到周六）
     */
    public static DateRange calculateLastWeekRange() {
        LocalDate today = LocalDate.now();
        
        // 计算上周日
        LocalDate lastSunday = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        
        // 计算上周六
        LocalDate lastSaturday = lastSunday.plusDays(6);
        
        return DateRange.builder()
                .startDate(lastSunday.format(DATE_FORMATTER))
                .endDate(lastSaturday.format(DATE_FORMATTER))
                .build();
    }

    /**
     * 计算本月的日期范围
     * 
     * @return 本月日期范围（第一天到最后一天）
     */
    public static DateRange calculateCurrentMonthRange() {
        LocalDate today = LocalDate.now();
        
        // 本月第一天
        LocalDate firstDay = today.with(TemporalAdjusters.firstDayOfMonth());
        
        // 本月最后一天
        LocalDate lastDay = today.with(TemporalAdjusters.lastDayOfMonth());
        
        return DateRange.builder()
                .startDate(firstDay.format(DATE_FORMATTER))
                .endDate(lastDay.format(DATE_FORMATTER))
                .build();
    }

    /**
     * 计算指定日期所在周的日期范围（周日到周六）
     * 
     * @param date 指定日期
     * @return 指定日期所在周的日期范围
     */
    public static DateRange calculateWeekRangeByDate(LocalDate date) {
        // 计算该日期所在周的周日
        LocalDate startOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
        
        // 计算该日期所在周的周六
        LocalDate endOfWeek = startOfWeek.plusDays(6);
        
        return DateRange.builder()
                .startDate(startOfWeek.format(DATE_FORMATTER))
                .endDate(endOfWeek.format(DATE_FORMATTER))
                .build();
    }

    /**
     * 计算指定开始日期和天数的日期范围
     * 
     * @param startDate 开始日期
     * @param days 天数
     * @return 日期范围
     */
    public static DateRange calculateDateRangeByDays(LocalDate startDate, int days) {
        LocalDate endDate = startDate.plusDays(days - 1);
        
        return DateRange.builder()
                .startDate(startDate.format(DATE_FORMATTER))
                .endDate(endDate.format(DATE_FORMATTER))
                .build();
    }
}