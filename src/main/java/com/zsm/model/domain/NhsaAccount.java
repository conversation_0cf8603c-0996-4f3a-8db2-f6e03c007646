package com.zsm.model.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 医保账户信息实体类
 *
 * <AUTHOR>
 * @date 2025/06/02
 */
@Data
public class NhsaAccount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 医疗机构编码
     */
    private String medicalCode;

    /**
     * 医疗机构
     */
    private String medicalName;

    /**
     * 操作员编码
     */
    private String operatorNo;

    /**
     * 操作员名称
     */
    private String operatorName;

    /**
     * 医保区划
     */
    private String area;

    /**
     * 医保局专线接口地址
     */
    private String baseUrl;

    private String downloadBaseUrl;

    /**
     * 删除标志（0代表正常,1代表删除）
     */
    private String deleteFlag;

}
