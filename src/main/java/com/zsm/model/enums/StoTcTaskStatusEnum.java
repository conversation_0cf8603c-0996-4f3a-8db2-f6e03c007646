package com.zsm.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举
 * 
 * <AUTHOR>
 * @since 2025-05-14
 */
@Getter
@AllArgsConstructor
public enum StoTcTaskStatusEnum {
    
    /** 待处理 */
    PENDING("0", "待处理"),
    
    /** 已完成 */
    COMPLETED("1", "已完成"),
    
    /** 已失效 */
    EXPIRED("2", "已失效");

    private final String code;
    private final String info;
    
    /**
     * 根据编码获取枚举
     * 
     * @param code 编码
     * @return 枚举对象
     */
    public static StoTcTaskStatusEnum getByCode(String code) {
        for (StoTcTaskStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 