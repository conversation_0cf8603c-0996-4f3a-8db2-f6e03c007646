package com.zsm.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 住院发药明细信息VO
 * 
 * 根据住院发药接口出参示例设计
 * 包含发药记录、药品信息、患者信息等完整明细数据
 * 
 * <AUTHOR>
 * @date 2025/6/11 15:56
 */
@NoArgsConstructor
@Data
@Schema(description = "住院发药明细信息")
public class InPatientDispenseDetailVo {

    // ============ 发药记录基本信息 ============
    @Schema(description = "发药记录ID", example = "3532698")
    @JsonProperty("record_id")
    private String recordId;

    @Schema(description = "发药明细ID", example = "70900882")
    @JsonProperty("record_detail_id")
    private String recordDetailId;

    @Schema(description = "原发药明细ID", example = "")
    @JsonProperty("ori_detail_id")
    private String oriDetailId;

    @Schema(description = "费用明细ID", example = "579050198")
    @JsonProperty("id_fee")
    private String idFee;

    @Schema(description = "费用名称", example = "整蛋白型肠内营养剂(粉剂)京")
    @JsonProperty("na_fee")
    private String naFee;

    @Schema(description = "医嘱类别", example = "1")
    @JsonProperty("sd_classify")
    private String sdClassify;

    @Schema(description = "发药单标记（0-发药；1-退药）", example = "0", allowableValues = {"0", "1"})
    @JsonProperty("fg_dps")
    private String fgDps;

    @Schema(description = "发药标志", example = "1")
    @JsonProperty("send_flag")
    private String sendFlag;

    @Schema(description = "发药时间", example = "2025-06-11 08:43:48")
    @JsonProperty("send_time")
    private String sendTime;

    @Schema(description = "零售单据号", example = "3532698")
    @JsonProperty("rtal_docno")
    private String rtalDocno;

    @Schema(description = "销售出库单据号", example = "3532698")
    @JsonProperty("stoout_no")
    private String stooutNo;

    // ============ 病区和科室信息 ============
    @Schema(description = "病区ID", example = "503")
    @JsonProperty("pat_ward_id")
    private String patWardId;

    @Schema(description = "病区名称", example = "呼吸与危重症医学科")
    @JsonProperty("pat_ward_name")
    private String patWardName;

    @Schema(description = "发药科室ID", example = "3")
    @JsonProperty("fyyf")
    private String fyyf;

    @Schema(description = "发药科室ID", example = "")
    @JsonProperty("dept_id")
    private String deptId;

    // ============ 药师信息 ============
    @Schema(description = "药师证件号码", example = "-")
    @JsonProperty("phar_certno")
    private String pharCertno;

    @Schema(description = "药师姓名", example = "董梦梦")
    @JsonProperty("phar_name")
    private String pharName;

    @Schema(description = "药师执业资格证号", example = "-")
    @JsonProperty("phar_prac_cert_no")
    private String pharPracCertNo;

    @Schema(description = "销售/退货时间", example = "2025-06-11 08:43:48")
    @JsonProperty("sel_retn_time")
    private String selRetnTime;

    // ============ 药品信息 ============
    @Schema(description = "HIS系统中的药品唯一编码", example = "1625-2219")
    @JsonProperty("his_drug_code")
    private String hisDrugCode;

    @Schema(description = "医疗目录编码", example = "XV01AAZ013P004010179067")
    @JsonProperty("med_list_codg")
    private String medListCodg;

    @Schema(description = "规格", example = "320g")
    @JsonProperty("spec")
    private String spec;

    @Schema(description = "生产企业名称", example = "德国Milupa GmbH")
    @JsonProperty("prodentp_name")
    private String prodentpName;

    @Schema(description = "定点医药机构目录编号", example = "1625-2219")
    @JsonProperty("fixmedins_hilist_id")
    private String fixmedinsHilistId;

    @Schema(description = "定点医药机构目录名称", example = "整蛋白型肠内营养剂(粉剂)京")
    @JsonProperty("fixmedins_hilist_name")
    private String fixmedinsHilistName;

    @Schema(description = "生产批号", example = "111448341")
    @JsonProperty("manu_lotnum")
    private String manuLotnum;

    @Schema(description = "生产日期", example = "2025-01-01 00:00:00")
    @JsonProperty("manu_date")
    private String manuDate;

    @Schema(description = "有效期止", example = "2026-11-25 00:00:00")
    @JsonProperty("expy_end")
    private String expyEnd;

    @Schema(description = "批次号", example = "111448341")
    @JsonProperty("bchno")
    private String bchno;

    @Schema(description = "处方药标志", example = "1")
    @JsonProperty("rx_flag")
    private String rxFlag;

    @Schema(description = "拆零标志", example = "1")
    @JsonProperty("trdn_flag")
    private String trdnFlag;

    @Schema(description = "HIS的剂量单位", example = "g")
    @JsonProperty("his_dos_unit")
    private String hisDosUnit;

    @Schema(description = "HIS的包装单位", example = "听")
    @JsonProperty("his_pac_unit")
    private String hisPacUnit;

    @Schema(description = "转换比", example = "1")
    @JsonProperty("his_con_ratio")
    private String hisConRatio;

    // ============ 患者及本次发放明细信息 ============
    @Schema(description = "定点医药机构批次流水号", example = "662755186-1944880-1625-2219")
    @JsonProperty("fixmedins_bchno")
    private String fixmedinsBchno;

    @Schema(description = "住院ID", example = "1944880")
    @JsonProperty("pat_in_hos_id")
    private String patInHosId;

    @Schema(description = "就医流水号", example = "34122025061085705234")
    @JsonProperty("mdtrt_sn")
    private String mdtrtSn;

    @Schema(description = "人员编号", example = "34120000000018182112")
    @JsonProperty("psn_no")
    private String psnNo;

    @Schema(description = "人员姓名（患者姓名）", example = "王彩芳")
    @JsonProperty("psn_name")
    private String psnName;

    @Schema(description = "床位号", example = "6017")
    @JsonProperty("bed_no")
    private String bedNo;

    @Schema(description = "就诊结算类型", example = "1")
    @JsonProperty("mdtrt_setl_type")
    private String mdtrtSetlType;

    @Schema(description = "医嘱ID", example = "662755186")
    @JsonProperty("order_id")
    private String orderId;

    @Schema(description = "开方医师证件号码", example = "")
    @JsonProperty("prsc_dr_certno")
    private String prscDrCertno;

    @Schema(description = "开方医师姓名", example = "见伟")
    @JsonProperty("prsc_dr_name")
    private String prscDrName;

    @Schema(description = "销售/退货数量", example = "1")
    @JsonProperty("sel_retn_cnt")
    private Integer selRetnCnt;

    @Schema(description = "销售/退货单位", example = "听")
    @JsonProperty("sel_retn_unit")
    private String selRetnUnit;

    @Schema(description = "医院医嘱主表唯一值", example = "662755186")
    @JsonProperty("cfxh")
    private String cfxh;

    @Schema(description = "医院医嘱明细表唯一值", example = "662755186")
    @JsonProperty("cfmxxh")
    private String cfmxxh;
}
