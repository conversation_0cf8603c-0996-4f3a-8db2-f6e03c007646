package com.zsm.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 药品追溯码上传结果响应类
 * <AUTHOR>
 * @date 2025/5/15 下午5:36
 */
@Data
@Schema(description = "药品追溯码上传结果响应类")
public class TraceabilityUploadResultVo implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 上传成功的处方ID列表
     */
    @Schema(description = "上传成功的处方ID列表")
    private List<String> success;
    
    /**
     * 上传失败的处方ID列表
     */
    @Schema(description = "上传失败的处方ID列表")
    private List<Object> fail;
    
    /**
     * 失败处方的错误信息，key为处方ID，value为错误信息
     */
    @Schema(description = "失败处方的错误信息，key为处方ID，value为错误信息")
    private Map<String, String> failMessages;
} 