package com.zsm.model.vo;

import com.zsm.entity.HisDrugDict;
import com.zsm.entity.YsfStoTcTaskSub;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务明细对象添加药品信息
 * <AUTHOR>
 * @date 2025/5/22 下午1:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class YsfStoTcTaskSubVo extends YsfStoTcTaskSub {
    @Schema(description = "药品字典")
    private HisDrugDict hisDrugDict;

    @Schema(description = "医疗目录编码")
    private String medListCodg;

    @Schema(description = "药品名称")
    private String drugName;

    @Schema(description = "药品规格")
    private String spec;

    @Schema(description = "生产企业名称")
    private String prodentpName;
}
