package com.zsm.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据处理结果VO
 * 用于封装出院患者药品数据比对和处理的结果
 * 
 * <AUTHOR>
 * @date 2025/8/3 21:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingDataResult {

    /**
     * 需要更新追溯码的数据列表
     * 这些数据已存在于3505表中，但缺少追溯码信息
     */
    @Builder.Default
    private List<InPatientDispenseDetailBindScatteredVo> updateTracCodeList = new ArrayList<>();

    /**
     * 需要新增的数据列表
     * 这些数据不存在于3505表中，需要从拆零池获取追溯码后插入
     */
    @Builder.Default
    private List<InPatientDispenseDetailBindScatteredVo> insertNewList = new ArrayList<>();

    /**
     * 所有需要处理的数据列表
     * 包含更新和新增的所有数据，用于后续的确认拆零信息操作
     */
    @Builder.Default
    private List<InPatientDispenseDetailBindScatteredVo> allProcessedData = new ArrayList<>();

    /**
     * 跳过处理的数据数量
     * 这些数据已经完整且无需处理
     */
    @Builder.Default
    private int skippedCount = 0;

    /**
     * 添加需要更新追溯码的数据
     * 
     * @param data 需要更新的数据
     */
    public void addUpdateTracCodeData(InPatientDispenseDetailBindScatteredVo data) {
        this.updateTracCodeList.add(data);
        this.allProcessedData.add(data);
    }

    /**
     * 添加需要新增的数据
     * 
     * @param data 需要新增的数据
     */
    public void addInsertNewData(InPatientDispenseDetailBindScatteredVo data) {
        this.insertNewList.add(data);
        this.allProcessedData.add(data);
    }

    /**
     * 增加跳过数量
     */
    public void incrementSkippedCount() {
        this.skippedCount++;
    }

    /**
     * 获取总处理数据数量
     * 
     * @return 总处理数据数量
     */
    public int getTotalProcessedCount() {
        return updateTracCodeList.size() + insertNewList.size();
    }

    /**
     * 判断是否有数据需要处理
     * 
     * @return 是否有数据需要处理
     */
    public boolean hasDataToProcess() {
        return !updateTracCodeList.isEmpty() || !insertNewList.isEmpty();
    }

    /**
     * 获取处理结果摘要信息
     * 
     * @return 处理结果摘要
     */
    public String getSummary() {
        return String.format("需要更新追溯码：%d条，需要新增：%d条，跳过：%d条，总处理：%d条",
                updateTracCodeList.size(), insertNewList.size(), skippedCount, getTotalProcessedCount());
    }
}
