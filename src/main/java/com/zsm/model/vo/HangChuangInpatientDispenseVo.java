package com.zsm.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 杭创住院发药接口响应VO
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@Schema(description = "杭创住院发药接口响应数据")
public class HangChuangInpatientDispenseVo {

    @Schema(description = "响应代码")
    private Integer code;

    @Schema(description = "响应消息")
    private String message;

    @Schema(description = "住院发药数据列表")
    private List<DispenseDetailItem> dataList;

    /**
     * 住院发药明细项
     */
    @Data
    @Schema(description = "住院发药明细项")
    public static class DispenseDetailItem {

        @Schema(description = "发药记录id")
        private String record_id;

        @Schema(description = "发药记录明细id")
        private String record_detail_id;

        @Schema(description = "原明细id")
        private String ori_detail_id;

        @Schema(description = "费用记录id")
        private String id_fee;

        @Schema(description = "费用名称")
        private String na_fee;

        @Schema(description = "医保分类")
        private String sd_classify;

        @Schema(description = "发药单标记")
        private String fg_dps;

        @Schema(description = "发送标志")
        private String send_flag;

        @Schema(description = "发送时间")
        private String send_time;

        @Schema(description = "零售单据号")
        private String rtal_docno;

        @Schema(description = "库存出库单号")
        private String stoout_no;

        @Schema(description = "患者病区id")
        private String pat_ward_id;

        @Schema(description = "患者病区名称")
        private String pat_ward_name;

        @Schema(description = "发药药房id")
        private String fyyf;

        @Schema(description = "发药药房id（与fyyf相同）")
        private String dept_id;

        @Schema(description = "药师证件号码")
        private String phar_certno;

        @Schema(description = "药师姓名")
        private String phar_name;

        @Schema(description = "药师执业证书编号")
        private String phar_prac_cert_no;

        @Schema(description = "售退时间")
        private String sel_retn_time;

        @Schema(description = "HIS药品编码")
        private String his_drug_code;

        @Schema(description = "药品目录编码")
        private String med_list_codg;

        @Schema(description = "规格")
        private String spec;

        @Schema(description = "生产企业名称")
        private String prodentp_name;

        @Schema(description = "定点医疗机构目录ID")
        private String fixmedins_hilist_id;

        @Schema(description = "定点医疗机构目录名称")
        private String fixmedins_hilist_name;

        @Schema(description = "生产批号")
        private String manu_lotnum;

        @Schema(description = "有效期截止")
        private String expy_end;

        @Schema(description = "批次号")
        private String bchno;

        @Schema(description = "处方标志")
        private String rx_flag;

        @Schema(description = "流转标志")
        private String trdn_flag;

        @Schema(description = "HIS剂量单位")
        private String his_dos_unit;

        @Schema(description = "HIS包装单位")
        private String his_pac_unit;

        @Schema(description = "HIS剂量换算比例")
        private String his_con_ratio;

        @Schema(description = "定点医疗机构批次号")
        private String fixmedins_bchno;

        @Schema(description = "住院号")
        private String pat_in_hos_id;

        @Schema(description = "就诊序列号")
        private String mdtrt_sn;

        @Schema(description = "人员姓名")
        private String psn_name;

        @Schema(description = "床位号")
        private String bed_no;

        @Schema(description = "就诊结算类型")
        private String mdtrt_setl_type;

        @Schema(description = "医嘱id")
        private String order_id;

        @Schema(description = "开方医师姓名")
        private String prsc_dr_name;

        @Schema(description = "售退数量")
        private Double sel_retn_cnt;

        @Schema(description = "售退单位")
        private String sel_retn_unit;

        @Schema(description = "处方序号")
        private String cfxh;

        @Schema(description = "处方明细序号")
        private String cfmxxh;
    }
} 