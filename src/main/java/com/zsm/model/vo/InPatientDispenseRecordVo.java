package com.zsm.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 住院发药记录VO
 * 
 * <AUTHOR>
 * @date 2025/6/11 17:56
 */
@NoArgsConstructor
@Data
@Schema(description = "住院发药记录信息")
public class InPatientDispenseRecordVo {

    @Schema(description = "发药记录ID", example = "3532522")
    @JsonProperty("record_id")
    private String recordId;

    @Schema(description = "病区ID", example = "5850")
    @JsonProperty("pat_ward_id")
    private String patWardId;

    @Schema(description = "病区名称", example = "肛肠盆底外科")
    @JsonProperty("pat_ward_name")
    private String patWardName;

    @Schema(description = "发药单标记（0-发药；1-退药）", example = "0", allowableValues = {"0", "1"})
    @JsonProperty("fg_dps")
    private String fgDps;

    @Schema(description = "发药标志", example = "1")
    @JsonProperty("send_flag")
    private String sendFlag;

    @Schema(description = "记录时间", example = "2025-06-11 00:27:28")
    @JsonProperty("record_time")
    private String recordTime;

    @Schema(description = "药师姓名", example = "段秉均")
    @JsonProperty("phar_name")
    private String pharName;

    @Schema(description = "摘要信息", example = "")
    @JsonProperty("summary")
    private String summary;
}
