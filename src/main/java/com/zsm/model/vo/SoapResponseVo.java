package com.zsm.model.vo;

import cn.hutool.json.JSONObject;
import lombok.Data;

/**
 * SOAP响应实体类
 * 用于封装SOAP Web Service调用的响应数据
 */
@Data
public class SoapResponseVo {
    
    /**
     * 原始SOAP响应内容
     */
    private String rawResponse;
    
    /**
     * 解析后的结果内容
     */
    private String result;
    
    /**
     * 解析后的JSON结果对象（当result为JSON格式时）
     */
    private JSONObject parsedResult;
    
    /**
     * 是否存在SOAP故障
     */
    private Boolean soapFault = false;
    
    /**
     * SOAP故障代码
     */
    private String faultCode;
    
    /**
     * SOAP故障信息
     */
    private String faultString;
    
    /**
     * 检查是否有SOAP故障
     * @return true：存在故障，false：无故障
     */
    public boolean hasSoapFault() {
        return Boolean.TRUE.equals(soapFault);
    }
    
    /**
     * 检查是否有有效的解析结果
     * @return true：有有效结果，false：无有效结果
     */
    public boolean hasValidResult() {
        return parsedResult != null || (result != null && !result.trim().isEmpty());
    }
    
    /**
     * 获取JSON格式的结果
     * 如果parsedResult存在则返回，否则尝试解析result
     * @return JSON对象，如果无法解析则返回null
     */
    public JSONObject getJsonResult() {
        if (parsedResult != null) {
            return parsedResult;
        }
        
        if (result != null && result.trim().startsWith("{")) {
            try {
                return new JSONObject(result);
            } catch (Exception e) {
                return null;
            }
        }
        
        return null;
    }
} 