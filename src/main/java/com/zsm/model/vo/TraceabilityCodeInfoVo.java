package com.zsm.model.vo;

import com.zsm.entity.HisDrugDict;
import com.zsm.entity.YsfStoTc;
import com.zsm.entity.YsfStoTcStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 追溯码信息VO，包含追溯码基本信息和生命周期记录
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@Schema(description = "追溯码信息VO，包含追溯码基本信息和生命周期记录")
public class TraceabilityCodeInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 追溯码信息
     */
    @Schema(description = "追溯码信息")
    private YsfStoTc traceabilityCodeInfo;

    /**
     * 药品字典
     */
    @Schema(description = "药品字典")
    private HisDrugDict hisDrugDict;
    /**
     * 状态历史记录列表，按create_time升序排序
     */
    @Schema(description = "状态历史记录列表，按create_time升序排序")
    private List<YsfStoTcStatus> statusHistory;


    @Schema(description = "医疗目录编码")
    private String medListCodg;

    @Schema(description = "药品名称")
    private String drugName;

    @Schema(description = "药品规格")
    private String spec;

    @Schema(description = "生产企业名称")
    private String prodentpName;
} 