package com.zsm.model.vo;

import lombok.Data;

import java.util.List;

/**
 * saas角色
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
public class SaasRole {

    private String createId;
    private String createBy;
    private String createTime;
    private String updateId;
    private String updateBy;
    private String updateTime;
    private String remark;
    private Long roleId;
    private String roleName;
    private String roleKey;
    private String roleSort;
    private String dataScope;
    private Boolean menuCheckStrictly;
    private Boolean deptCheckStrictly;
    private String status;
    private String delFlag;
    private Boolean flag;
    private String menuIds;
    private String deptIds;
    private List<String> permissions;
    private Boolean admin;

}