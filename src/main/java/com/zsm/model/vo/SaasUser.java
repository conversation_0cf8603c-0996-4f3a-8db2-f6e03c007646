package com.zsm.model.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * saas用户
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
public class SaasUser {

    private String fyyf;
    private String createId;
    private String createBy;
    private String createTime;
    private String updateId;
    private String updateBy;
    private String updateTime;
    private String remark;
    private Long userId;
    private Long deptId;
    private String userName;
    private String nickName;
    private Integer orgType;
    private String orgId;
    private String orgName;
    private String email;
    private String phonenumber;
    private String sex;
    private String avatar;
    private String password;
    private Long isImportca;
    private String belongArea;
    private String status;
    private String medinsDepartment;
    private String delFlag;
    private String loginIp;
    private Date loginDate;
    private SaasDept dept;
    private List<SaasRole> roles;
    private String roleIds;
    private String postIds;
    private Long roleId;
    private String userCardId;
    private String fullName;
    private Boolean admin;
}