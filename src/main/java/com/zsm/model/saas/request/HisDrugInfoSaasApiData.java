package com.zsm.model.saas.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * his药品信息
 *
 * <AUTHOR>
 * @date 2025/06/14
 */
@Data
public class HisDrugInfoSaasApiData implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * HIS药品ID
     */
    private String hisDrugId;
    /**
     * HIS药品名称
     */
    private String hisDrugName;

    /**
     * HIS最小单位
     */
    private String hisMinUnit;
    /**
     * HIS包装单位
     */
    private String hisPackageUnit;
    /**
     * HIS系数
     */
    private BigDecimal hisFactor;

    /**
     * HIS配送企业编码
     */
    private String hisEnterpriseCode;
    /**
     * HIS配送企业名称
     */
    private String hisEnterpriseName;

    /**
     * HIS国家药品代码
     */
    private String hisDrugCountryCode;
    /**
     * HIS国家标准名称
     */
    private String hisDrugCountryName;

    /**
     * HIS规格
     */
    private String hisDrugSpec;

    /**
     * HIS生产企业编码
     */
    private String hisDrugManufacturerCode;
    /**
     * HIS生产企业
     */
    private String hisDrugManufacturerName;

    /**
     * HIS采购单位
     */
    private String hisPurchaseUnit;
    /**
     * 采购价格
     */
    private BigDecimal hisPurchasePrice;
    /**
     * 剂型
     */
    private String hisDoseForm;
    /**
     * 批准文号
     */
    private String hisApprovalNum;
    /**
     * 规格
     */
    private String hisPac;

    /**
     * 最小剂量单位
     */
    private String hisDosUnit;
    /**
     * 最小包装单位
     */
    private String hisPacUnit;
    /**
     * 转换比
     */
    private BigDecimal hisConRatio;
    /**
     * 整件数量
     */
    private BigDecimal wholeQuantity;
    /**
     * 备注
     */
    private String memo;

    /**
     * 包装单位
     */
    private String hisPackUnit;
    /**
     * 折扣率
     */
    private BigDecimal hisDiscRate;
    /**
     * 是否删除
     */
    private Integer delFlag;

}
