package com.zsm.model.saas.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 获取药品追溯码库存数据响应
 */
@Data
public class GetTracCodgStoreDataResponse {

    /**
     * 药品编码
     */
    private String drugCode;
    
    /**
     * 发药数量
     */
    private String dispCnt;
    
    /**
     * 药品追溯码列表
     */
    private List<String> drugTracCodgs;
    
    /**
     * 处方序号
     */
    private String cfxh;
    
    /**
     * 处方明细序号
     */
    private String cfmxxh;
    
    /**
     * 当前库存数量
     */
    private BigDecimal currNum;

}