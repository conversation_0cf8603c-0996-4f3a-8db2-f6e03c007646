package com.zsm.model.nhsa.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 医保费用明细出参实体对象
 * 
 * 医保接口5204费用明细查询的响应结果
 * 包含医疗费用的详细信息，如药品信息、收费项目、价格、金额等
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
@NoArgsConstructor
@Data
@Schema(description = "医保费用明细信息")
public class Fsi5204Response {

    // ============ 基本信息 ============
    @Schema(description = "就诊ID", example = "34122025060184573770")
    @JsonProperty("mdtrt_id")
    private String mdtrtId;

    @Schema(description = "结算ID", example = "34122025061066477635")
    @JsonProperty("setl_id")
    private String setlId;

    @Schema(description = "费用明细流水号", example = "123782417")
    @JsonProperty("feedetl_sn")
    private String feedetlSn;

    @Schema(description = "处方/医嘱号", example = "578947284")
    @JsonProperty("rx_drord_no")
    private String rxDrordNo;

    @Schema(description = "支付地点类别", example = "2")
    @JsonProperty("payLoc")
    private String payLoc;

    @Schema(description = "医疗类别", example = "21")
    @JsonProperty("med_type")
    private String medType;

    @Schema(description = "费用发生时间", example = "2025-06-11 08:43:48")
    @JsonProperty("fee_ocur_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime feeOcurTime;

    // ============ 数量和价格信息 ============
    @Schema(description = "数量", example = "1")
    @JsonProperty("cnt")
    private BigDecimal cnt;

    @Schema(description = "单价", example = "6.7")
    @JsonProperty("pric")
    private BigDecimal pric;

    @Schema(description = "单次剂量描述", example = "")
    @JsonProperty("sin_dos_dscr")
    private String sinDosDscr;

    @Schema(description = "使用频次描述", example = "")
    @JsonProperty("used_frqu_dscr")
    private String usedFrquDscr;

    @Schema(description = "周期天数", example = "")
    @JsonProperty("prd_days")
    private BigDecimal prdDays;

    @Schema(description = "用药途径描述", example = "")
    @JsonProperty("medc_way_dscr")
    private String medcWayDscr;

    // ============ 费用金额信息 ============
    @Schema(description = "明细项目费用总额", example = "6.7")
    @JsonProperty("det_item_fee_sumamt")
    private BigDecimal detItemFeeSumamt;

    @Schema(description = "定价上限金额", example = "7")
    @JsonProperty("pric_uplmt_amt")
    private BigDecimal pricUplmtAmt;

    @Schema(description = "自付比例", example = "0")
    @JsonProperty("selfpay_prop")
    private BigDecimal selfpayProp;

    @Schema(description = "全自费金额", example = "0")
    @JsonProperty("fulamt_ownpay_amt")
    private BigDecimal fulamtOwnpayAmt;

    @Schema(description = "超限价金额", example = "0")
    @JsonProperty("overlmt_amt")
    private BigDecimal overlmtAmt;

    @Schema(description = "先行自付金额", example = "0")
    @JsonProperty("preselfpay_amt")
    private BigDecimal preselfpayAmt;

    @Schema(description = "符合政策范围金额", example = "6.7")
    @JsonProperty("inscp_scp_amt")
    private BigDecimal inscpScpAmt;

    // ============ 医保目录信息 ============
    @Schema(description = "收费项目等级", example = "01")
    @JsonProperty("chrgitm_lv")
    private String chrgitmlv;

    @Schema(description = "医保目录编码", example = "001204000020100-ABBB0001")
    @JsonProperty("hilist_code")
    private String hilistCode;

    @Schema(description = "医保目录名称", example = "静脉采血")
    @JsonProperty("hilist_name")
    private String hilistName;

    @Schema(description = "目录类别", example = "201")
    @JsonProperty("list_type")
    private String listType;

    @Schema(description = "医疗目录编码", example = "001204000020100-ABBB0001")
    @JsonProperty("med_list_codg")
    private String medListCodg;

    @Schema(description = "医药机构目录编码", example = "001204000020100-ABBB0001")
    @JsonProperty("medins_list_codg")
    private String medinsListCodg;

    @Schema(description = "医药机构目录名称", example = "静脉采血")
    @JsonProperty("medins_list_name")
    private String medinsListName;

    @Schema(description = "医疗收费项目类别", example = "09")
    @JsonProperty("med_chrgitm_type")
    private String medChrgitmType;

    // ============ 药品信息 ============
    @Schema(description = "商品名", example = "")
    @JsonProperty("prodname")
    private String prodname;

    @Schema(description = "规格", example = "")
    @JsonProperty("spec")
    private String spec;

    @Schema(description = "剂型名称", example = "")
    @JsonProperty("dosform_name")
    private String dosformName;

    // ============ 科室和医生信息 ============
    @Schema(description = "开单科室编码", example = "230")
    @JsonProperty("bilg_dept_codg")
    private String bilgDeptCodg;

    @Schema(description = "开单科室名称", example = "妇科")
    @JsonProperty("bilg_dept_name")
    private String bilgDeptName;

    @Schema(description = "开单医生编码", example = "D341202017446")
    @JsonProperty("bilg_dr_codg")
    private String bilgDrCodg;

    @Schema(description = "开单医师姓名", example = "妇科")
    @JsonProperty("bilg_dr_name")
    private String bilgDrName;

    @Schema(description = "受单科室编码", example = "230")
    @JsonProperty("acord_dept_codg")
    private String acordDeptCodg;

    @Schema(description = "受单科室名称", example = "妇科")
    @JsonProperty("acord_dept_name")
    private String acordDeptName;

    @Schema(description = "受单医生编码", example = "")
    @JsonProperty("orders_dr_code")
    private String ordersDrCode;

    @Schema(description = "受单医生姓名", example = "")
    @JsonProperty("orders_dr_name")
    private String ordersDrName;

    // ============ 标志信息 ============
    @Schema(description = "限制使用标志", example = "0")
    @JsonProperty("lmt_used_flag")
    private String lmtUsedFlag;

    @Schema(description = "医院制剂标志", example = "")
    @JsonProperty("hosp_prep_flag")
    private String hospPrepFlag;

    @Schema(description = "医院审批标志", example = "1")
    @JsonProperty("hosp_appr_flag")
    private String hospApprFlag;

    @Schema(description = "中药使用方式", example = "1")
    @JsonProperty("tcmdrug_used_way")
    private String tcmdrugUsedWay;

    @Schema(description = "生产地类别", example = "")
    @JsonProperty("prodplac_type")
    private String prodplacType;

    @Schema(description = "基本药物标志", example = "0")
    @JsonProperty("bas_medn_flag")
    private String basMednFlag;

    @Schema(description = "医保谈判药品标志", example = "0")
    @JsonProperty("hi_nego_drug_flag")
    private String hiNegoDrugFlag;

    @Schema(description = "儿童用药标志", example = "")
    @JsonProperty("chld_medc_flag")
    private String chldMedcFlag;

    @Schema(description = "外检标志", example = "0")
    @JsonProperty("etip_flag")
    private String etipFlag;

    @Schema(description = "外检医院编码", example = "")
    @JsonProperty("etip_hosp_code")
    private String etipHospCode;

    @Schema(description = "出院带药标志", example = "0")
    @JsonProperty("dscg_tkdrug_flag")
    private String dscgTkdrugFlag;

    @Schema(description = "目录特项标志", example = "")
    @JsonProperty("list_sp_item_flag")
    private String listSpItemFlag;

    @Schema(description = "生育费用标志", example = "0")
    @JsonProperty("matn_fee_flag")
    private String matnFeeFlag;

    @Schema(description = "直报标志", example = "0")
    @JsonProperty("drt_reim_flag")
    private String drtReimFlag;

    // ============ 操作信息 ============
    @Schema(description = "备注", example = "")
    @JsonProperty("memo")
    private String memo;

    @Schema(description = "经办人ID", example = "E0C5E0911C4081C4")
    @JsonProperty("opter_id")
    private String opterId;

    @Schema(description = "经办人姓名", example = "太和县人民医院")
    @JsonProperty("opter_name")
    private String opterName;

    @Schema(description = "经办时间", example = "2025-06-11 08:45:11")
    @JsonProperty("opt_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime optTime;

    @Schema(description = "收费批次号", example = "")
    @JsonProperty("chrg_bchno")
    private String chrgBchno;
}