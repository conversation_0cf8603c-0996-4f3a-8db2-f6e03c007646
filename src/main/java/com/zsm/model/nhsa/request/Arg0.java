package com.zsm.model.nhsa.request;

import lombok.Data;

/**
 * 序号	数据元标识	数据元名称	类型	长度	代码标识	是否必填	备注
 * 1	infno	交易编号	字符型	4		Y	交易编号详见接口列表
 * 2	msgid	发送方报文ID	字符型	30		Y	定点医药机构编号(12)+时间(14)+顺序号(4) 时间格式：yyyyMMddHHmmss
 * 3	mdtrtarea_admvs	就医地医保区划	字符型	6		Y
 * 4	insuplc_admdvs	参保地医保区划	字符型	6			如果交易输入中含有人员编号，此项必填，可通过【1101】人员信息获取交易取得
 * 5	recer_sys_code	接收方系统代码	字符型	10		Y	用于多套系统接入，区分不同系统使用
 * 6	dev_no	设备编号	字符型	100
 * 7	dev_safe_info	设备安全信息	字符型	2000
 * 8	cainfo	数字签名信息	字符型	1024
 * 9	signtype	签名类型	字符型	10			建议使用SM2、SM3
 * 10	infver	接口版本号	字符型	6		Y	例如：“V1.0”，版本号由医保下发通知。
 * 11	opter_type	经办人类别		3	Y	Y	1-经办人；2-自助终端；3-移动终端
 * 12	opter	经办人	字符型	30		Y	按地方要求传入经办人/终端编号
 * 13	opter_name	经办人姓名	字符型	50		Y	按地方要求传入经办人姓名/终端名称
 * 14	inf_time	交易时间	日期时间型	19		Y
 * 15	fixmedins_code	定点医药机构编号	字符型	12		Y
 * 16	fixmedins_name	定点医药机构名称	字符型	20		Y
 * 17	sign_no	交易签到流水号	字符型	30			通过签到【9001】交易获取
 * 18	app_id	渠道id	字符型	32
 * 19	enc_type	加密方式	字符型	6		Y	不加密传空，加密传SM4
 * 20	input	交易输入	字符型	40000		Y
 * 21	pw_ecToken	电子凭证密码核验token	字符型	50			用于调用电子凭证相应交易保存token使用
 */
@Data
public class Arg0<T> {
    private String app_id;
    private String cainfo;
    private String dev_no;
    private String dev_safe_info;
    private String fixmedins_code;
    private String fixmedins_name;
    private String inf_time;
    private String infno;
    private String infver;
    private T input;
    private String insuplc_admdvs;
    private String mdtrtarea_admvs;
    private String msgid;
    private String opter;
    private String opter_name;
    private String opter_type;
    private String pw_ecToken;
    private String recer_sys_code;
    private String sign_no;
    private String signtype;

}