package com.zsm.model.nhsa.request.fsi3505;

import com.zsm.model.nhsa.request.DrugTracInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 3505销售入参报文
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Selinfo3505 {
    /** 医疗目录编码 */
    private String med_list_codg;
    /** 定点医药机构目录编号 */
    private String fixmedins_hilist_id;
    /** 定点医药机构目录名称 */
    private String fixmedins_hilist_name;
    /** 定点医药机构批次流水号 */
    private String fixmedins_bchno;
    /** 开方医师证件类型 */
    private String prsc_dr_cert_type;
    /** 开方医师证件号码 */
    private String prsc_dr_certno;
    /** 开方医师姓名 */
    private String prsc_dr_name;
    /** 药师证件类型 */
    private String phar_cert_type;
    /** 药师证件号码 */
    private String phar_certno;
    /** 药师姓名 */
    private String phar_name;
    /** 药师执业资格证号 */
    private String phar_prac_cert_no;
    /** 医保费用结算类型 */
    private String hi_feesetl_type;
    /** 结算ID */
    private String setl_id;
    /** 就医流水号 */
    private String mdtrt_sn;
    /** 人员编号 */
    private String psn_no;
    /** 人员证件类型 */
    private String psn_cert_type;
    /** 证件号码 */
    private String certno;
    /** 人员姓名 */
    private String psn_name;
    /** 生产批号 */
    private String manu_lotnum;

    /** 生产日期 */
    private LocalDate manu_date;

    /** 有效期止 */
    private LocalDate expy_end;

    /** 处方药标志 */
    private String rx_flag;
    /** 拆零标志 */
    private String trdn_flag;
    /** 最终成交单价 */
    private BigDecimal finl_trns_pric;
    /** 处方号 */
    private String rxno;
    /** 外购处方标志 */
    private String rx_circ_flag;
    /** 零售单据号 */
    private String rtal_docno;
    /** 销售出库单据号 */
    private String stoout_no;
    /** 批次号 */
    private String bchno;
    /** 药品追溯码（弃用） */
    private String drug_trac_codg;
    /** 药品条形码 */
    private String drug_prod_barc;
    /** 货架位 */
    private String shelf_posi;
    /** 销售/退货数量 */
    private BigDecimal sel_retn_cnt;

    /** 销售/退货时间 */
    private LocalDateTime sel_retn_time;
    /** 销售/退货经办人姓名 */
    private String sel_retn_opter_name;
    /** 备注 */
    private String memo;
    /** 就诊结算类型 */
    private String mdtrt_setl_type;
    /** 溯源码节点信息 */
    private List<DrugTracInfo> drugtracinfo;

}