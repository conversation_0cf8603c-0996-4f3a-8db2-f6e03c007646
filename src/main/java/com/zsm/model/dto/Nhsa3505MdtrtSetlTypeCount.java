package com.zsm.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class Nhsa3505MdtrtSetlTypeCount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 医疗机构编码
     */
    private String medicalCode;

    /**
     * 医疗机构
     */
    private String medicalName;

    /**
     * 统计日期
     */
    private String countDate;

    /**
     * cnt总数量
     */
    private Integer totalCount;

    /**
     * 追溯码数量
     */
    private Integer traceCodeCount;

    /**
     * 所有数据cnt数量,以及没有追溯码的
     */
    private Integer allCntCount;

    /**
     * 所有数据条数
     */
    private Integer allDataNumber;

    /**
     * 所有含追溯码的数据条数
     */
    private Integer traceCodeDataNumber;

    /**
     * 医保结算类型
     */
    private String mdtrtSetlType;

    /**
     * 删除标志（0代表正常,1代表删除）
     */
    private String deleteFlag;
    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}
