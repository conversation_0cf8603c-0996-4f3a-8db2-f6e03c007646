package com.zsm.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NhsaHospitalUploadCount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 医疗机构编码
     */
    private String medicalCode;

    /**
     * 医疗机构
     */
    private String medicalName;

    /**
     * 两定接口编码
     */
    private String apiCode;

    /**
     * 上传数据的数量
     */
    private Integer dataCount;

    /**
     * 上传追溯码的数量
     */
    private Integer drugTracCodgCount;

    /**
     * 今天之前的上传数据的数量
     */
    private Integer dataCountBeforeToday;

    /**
     * 今天之前的上传追溯码的数量
     */
    private Integer drugTracCodgCountBeforeToday;

    /**
     * 昨天的上传数据的数量
     */
    private Integer dataCountYesterday;

    /**
     * 昨天的上传追溯码的数量
     */
    private Integer drugTracCodgCountYesterday;

    /**
     * 删除标志（0代表正常,1代表删除）
     */
    private String deleteFlag;


    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private String remark;


}
