package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 扫码任务列表查询请求参数
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
@Schema(description = "扫码任务列表查询请求参数")
public class YsfStoTcTaskQueryDto {

    @Schema(description = "任务状态 (fg_status)")
    private String taskStatus;

    @Schema(description = "患者姓名")
    private String remark;

    @Schema(description = "业务单号 (cd_biz 即 outPresId)")
    private String bizCode;

    @Schema(description = "任务类型 (sd_task_type)")
    private String taskType;

    @Schema(description = "创建开始时间")
    private String startTime;

    @Schema(description = "创建结束时间")
    private String endTime;
    @NotNull(message = "页码不能为空")
    @Schema(description = "页码", example = "1")
    private Integer pageNum;
    @NotNull(message = "每页数量不能为空")
    @Schema(description = "每页数量", example = "10")
    private Integer pageSize;
} 