package com.zsm.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ZsmView3505 {
    private String medListCodg;
    private String fixmedinsHilistId;
    private String fixmedinsHilistName;
    private String fixmedinsBchno;
    private String prscDrName;
    private String pharName;
    private String pharPracCertNo;
    private String rxFlag;
    private String trdnFlag;
    private String rtalDocno;
    private BigDecimal selRetnCnt;


    private String selRetnOpterName;
    private String mdtrtSetlType;
    private String manuLotnum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date selRetnTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date manuDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expyEnd;

    private String mdtrtSn;

    private String psnName;

    private String prscDrCertType;

    private String prscDrCertno;

    private String pharCertType;

    private String pharCertno;

    private String hiFeesetlType;

    private String setlId;

    private String psnNo;

    private String psnCertType;

    private String certno;

    private BigDecimal finlTrnsPric;

    private String rxno;

    private String rxCircFlag;

    private String stooutNo;
    private String bchno;
    private String drugProdBarc;
    private String shelfPosi;
    private String memo;

    private String sjh;
    private String cfxh;
    private String cfmxxh;
    private String patientId;
    private String patCardNo;

    private BigDecimal hisConRatio;
    private BigDecimal minUnitSelRetnCnt;

    private String cydybz; // cydybz--出院带药标志1 出院带药 0正常医嘱

    private String hisUniqueKey;

    private String fyyf;

}