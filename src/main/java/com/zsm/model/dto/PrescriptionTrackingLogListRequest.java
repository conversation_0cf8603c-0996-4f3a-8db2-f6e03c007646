package com.zsm.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class PrescriptionTrackingLogListRequest {

    private List<PrescriptionTrackingLog> dataList;

    /**
     * 医疗机构代码
     */
    private String medicalCode;

    /**
     * 医疗机构名称
     */
    private String medicalName;

    private String shelfCode;

    /**
     * 患者姓名
     */
    private String psnName;

    /**
     * 窗口号
     */
    private String dispenWindows;

    /**
     * 可以和处方关联起来的字段(处方序号、收据号、门诊号具体医院的策略来定)
     */
    private String cfxh;

    /**
     * status: pending(正常)、processing（发药中）
     */
    private String status;

    private String remark;


}
