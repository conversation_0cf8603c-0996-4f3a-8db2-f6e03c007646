package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 住院处方查询请求DTO
 * 
 * 根据住院发药接口入参参数说明设计
 * 支持按发药记录ID、时间范围、病区等条件查询住院发药明细信息
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
@Schema(description = "住院处方查询请求参数")
public class InpatientPrescriptionQueryDto {

    @Schema(description = "发药药房ID", example = "3", maxLength = 50)
    private String fyyf;

    @Schema(description = "发药科室ID（与fyyf意义相同，任选一个或同时提供）", example = "3", maxLength = 50)
    private String deptId;

    @Schema(description = "开始时间（与endTime配合使用，查询某个时间范围内的发药记录）", 
            example = "2025-06-11 00:00:00", 
            pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @Schema(description = "结束时间（与startTime配合使用。如果提供了recordId，则忽略时间范围）", 
            example = "2025-06-11 23:59:59", 
            pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @Schema(description = "患者病区id（配合筛选）", example = "5258", maxLength = 50)
    private String patWardId;

    @Schema(description = "发药记录ID（发药单号/退药单号，如果提供此参数将优先使用，忽略时间范围）",
            example = "3532698")
    private String recordId;
    @Schema(description = "住院号(查出院带药)", example = "123456")
    private String patInHosId;

    @Schema(description = "发药单标记（必填）", 
            example = "0", 
            allowableValues = {"0", "1"},
            maxLength = 4,
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String fgDps;

    /**
     * 默认构造函数
     */
    public InpatientPrescriptionQueryDto() {
        // 设置默认值
        this.fyyf = "";
        this.deptId = "";
        this.startTime = "";
        this.endTime = "";
        this.patWardId = "";
        this.recordId = "";
        this.fgDps = "0"; // 默认为发药
    }

    /**
     * 带发药记录ID的构造函数
     * 
     * @param recordId 发药记录ID
     */
    public InpatientPrescriptionQueryDto(String recordId) {
        this();
        this.recordId = recordId;
    }

    /**
     * 带发药记录ID和发药标记的构造函数
     * 
     * @param recordId 发药记录ID
     * @param fgDps 发药单标记（0-发药；1-退药）
     */
    public InpatientPrescriptionQueryDto(String recordId, String fgDps) {
        this();
        this.recordId = recordId;
        this.fgDps = fgDps;
    }
} 