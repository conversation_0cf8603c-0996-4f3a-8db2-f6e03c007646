package ${package.Entity};

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * ${table.comment}实体类
 * 
 * <AUTHOR>
 * @since ${date}
 */
@Data
@TableName("${schemaName}${table.name}")
@Schema(description = "${table.comment}")
public class ${entity} implements Serializable {

    private static final long serialVersionUID = 1L;

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})

    /**
     * ${field.comment}
     */
    @Schema(description = "${field.comment}")
#if(${field.propertyType} == "String")
#if(${field.propertyName} == "password")
    @NotBlank(message = "${field.comment}不能为空")
    @Size(min = 6, max = 100, message = "${field.comment}长度必须在6-100个字符之间")
#elseif($field.propertyName.contains("name") || $field.propertyName.contains("Name"))
    @NotBlank(message = "${field.comment}不能为空")
    @Size(max = 30, message = "${field.comment}长度不能超过30个字符")
#elseif($field.propertyName.contains("key") || $field.propertyName.contains("Key"))
    @NotBlank(message = "${field.comment}不能为空")
    @Size(max = 100, message = "${field.comment}长度不能超过100个字符")
#elseif(${field.propertyName} == "remark")
    @Size(max = 500, message = "${field.comment}长度不能超过500个字符")
#else
    @Size(max = 100, message = "${field.comment}长度不能超过100个字符")
#end
#end
#if(${field.fill})
## -----   存在字段填充设置   -----
#if(${field.customMap.fieldFill})
    @TableField(value = "${field.annotationColumnName}", fill = FieldFill.${field.customMap.fieldFill})
#else
    @TableField(value = "${field.annotationColumnName}", fill = FieldFill.${field.fill})
#end
#else
    @TableField("${field.annotationColumnName}")
#end
    private ${field.propertyType} ${field.propertyName};
#end
## ----------  END 字段循环遍历  ----------
} 