<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.YsfStoTcTaskSubMapper">

        <!-- 根据条件查询扫码任务明细分页列表 -->
    <select id="selectTaskSubList" parameterType="com.zsm.model.dto.YsfStoTcTaskSubQueryDto" resultType="com.zsm.entity.YsfStoTcTaskSub">
        SELECT
            *
        FROM
            ysf_sto_tc_task_sub
        <where>
            del_flag = '0'
            AND id_task = #{queryDto.idTask}
            <if test="queryDto.drugNameOrCode != null and queryDto.drugNameOrCode != ''">
                AND drug_code LIKE CONCAT('%', #{queryDto.drugNameOrCode}, '%')
            </if>
            <if test="queryDto.isScanned != null and queryDto.isScanned != ''">
                AND fg_scanned = #{queryDto.isScanned}
            </if>
            <if test="queryDto.traceCode != null and queryDto.traceCode != ''">
                AND drugtracinfo LIKE CONCAT('%', #{queryDto.traceCode}, '%')
            </if>
        </where>
        ORDER BY id_sub DESC
    </select>
</mapper>
