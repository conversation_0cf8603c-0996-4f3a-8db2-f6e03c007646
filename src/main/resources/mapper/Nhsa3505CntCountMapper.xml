<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.Nhsa3505CntCountMapper">
 <resultMap type="Nhsa3505CntCount" id="Nhsa3505CntCountResult">
        <result property="id" column="id"/>
        <result property="medicalCode" column="medical_code"/>
        <result property="medicalName" column="medical_name"/>
        <result property="countDate" column="count_date"/>
        <result property="totalCount" column="total_count"/>
        <result property="traceCodeCount" column="trace_code_count"/>
        <result property="allCntCount" column="all_cnt_count"/>
        <result property="allDataNumber" column="all_data_number"/>
        <result property="traceCodeDataNumber" column="trace_code_data_number"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deleteFlag" column="delete_flag"/>
    </resultMap>

    <sql id="selectNhsa3505CntCountVo">
        select id,
               medical_code,
               medical_name,
               count_date,
               total_count,
               trace_code_count,
               all_cnt_count,
               all_data_number,
               trace_code_data_number,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               delete_flag
        from nhsa_3505_cnt_count
    </sql>

    <select id="selectNhsa3505CntCountList" parameterType="Nhsa3505CntCount" resultMap="Nhsa3505CntCountResult">
        select
               medical_code,
               medical_name,
               count_date,
               total_count,
               trace_code_count,
               all_cnt_count,
               all_data_number,
               trace_code_data_number,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               delete_flag
        from nhsa_3505_cnt_count
        <where>
            <if test="medicalCode != null  and medicalCode != ''">and medical_code = #{medicalCode}</if>
            <if test="medicalName != null  and medicalName != ''">and medical_name like concat('%', #{medicalName},
                '%')
            </if>
            <if test="countDate != null  and countDate != ''">and count_date = #{countDate}</if>
            <if test="totalCount != null ">and total_count = #{totalCount}</if>
            <if test="traceCodeCount != null ">and trace_code_count = #{traceCodeCount}</if>
            <if test="allCntCount != null ">and all_cnt_count = #{allCntCount}</if>
            <if test="allDataNumber != null ">and all_data_number = #{allDataNumber}</if>
            <if test="traceCodeDataNumber != null ">and trace_code_data_number = #{traceCodeDataNumber}</if>
            <if test="deleteFlag != null  and deleteFlag != ''">and delete_flag = #{deleteFlag}</if>
        </where>
    </select>

    <select id="selectNhsa3505CntCountById" parameterType="Long" resultMap="Nhsa3505CntCountResult">
        <include refid="selectNhsa3505CntCountVo"/>
        where id = #{id}
    </select>

    <insert id="insertNhsa3505CntCount" parameterType="Nhsa3505CntCount" useGeneratedKeys="true" keyProperty="id">
        insert into nhsa_3505_cnt_count
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="medicalCode != null">medical_code,</if>
            <if test="medicalName != null">medical_name,</if>
            <if test="countDate != null">count_date,</if>
            <if test="totalCount != null">total_count,</if>
            <if test="traceCodeCount != null">trace_code_count,</if>
            <if test="allCntCount != null">all_cnt_count,</if>
            <if test="allDataNumber != null">all_data_number,</if>
            <if test="traceCodeDataNumber != null">trace_code_data_number,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deleteFlag != null">delete_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="medicalCode != null">#{medicalCode},</if>
            <if test="medicalName != null">#{medicalName},</if>
            <if test="countDate != null">#{countDate},</if>
            <if test="totalCount != null">#{totalCount},</if>
            <if test="traceCodeCount != null">#{traceCodeCount},</if>
            <if test="allCntCount != null">#{allCntCount},</if>
            <if test="allDataNumber != null">#{allDataNumber},</if>
            <if test="traceCodeDataNumber != null">#{traceCodeDataNumber},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
        </trim>
    </insert>

    <update id="updateNhsa3505CntCount" parameterType="Nhsa3505CntCount">
        update nhsa_3505_cnt_count
        <trim prefix="SET" suffixOverrides=",">
            <if test="medicalCode != null">medical_code = #{medicalCode},</if>
            <if test="medicalName != null">medical_name = #{medicalName},</if>
            <if test="countDate != null">count_date = #{countDate},</if>
            <if test="totalCount != null">total_count = #{totalCount},</if>
            <if test="traceCodeCount != null">trace_code_count = #{traceCodeCount},</if>
            <if test="allCntCount != null">all_cnt_count = #{allCntCount},</if>
            <if test="allDataNumber != null">all_data_number = #{allDataNumber},</if>
            <if test="traceCodeDataNumber != null">trace_code_data_number = #{traceCodeDataNumber},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNhsa3505CntCountById" parameterType="Long">
        delete
        from nhsa_3505_cnt_count
        where id = #{id}
    </delete>

    <delete id="deleteNhsa3505CntCountByIds" parameterType="String">
        delete from nhsa_3505_cnt_count where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectNhsa3505CntCountListByDateRange" resultMap="Nhsa3505CntCountResult">
        select
               medical_code,
               medical_name,
               count_date,
               total_count,
               trace_code_count,
               all_cnt_count,
               all_data_number,
               trace_code_data_number,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               delete_flag
        from nhsa_3505_cnt_count
        <where>
            <if test="startDate != null and startDate != ''">
                and count_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and count_date &lt;= #{endDate}
            </if>
            and (delete_flag = '0' or delete_flag is null)
        </where>
        order by count_date desc
    </select>
</mapper>
