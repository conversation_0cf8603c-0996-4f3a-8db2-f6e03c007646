<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.YsfStoTcStatusMapper">

    <!-- 根据条件查询追溯码状态记录分页列表 -->
    <select id="selectStatusRecordList" parameterType="com.zsm.model.dto.YsfStoTcStatusQueryDto" resultType="com.zsm.entity.YsfStoTcStatus">
        SELECT
            *
        FROM
            ysf_sto_tc_status
        <where>
            del_flag = '0'
            <if test="queryDto.drugtracinfo != null and queryDto.drugtracinfo != ''">
                AND drugtracinfo = #{queryDto.drugtracinfo}
            </if>
            <if test="queryDto.sdTcStatus != null and queryDto.sdTcStatus != ''">
                AND sd_tc_status = #{queryDto.sdTcStatus}
            </if>
            <if test="queryDto.idSto != null and queryDto.idSto != ''">
                AND id_sto = #{queryDto.idSto}
            </if>
            <if test="queryDto.drugCode != null and queryDto.drugCode != ''">
                AND drug_code = #{queryDto.drugCode}
            </if>
            <if test="queryDto.idBizOri != null and queryDto.idBizOri != ''">
                AND id_biz_ori = #{queryDto.idBizOri}
            </if>
            <if test="queryDto.beginTime != null">
                AND create_time &gt;= #{queryDto.beginTime}
            </if>
            <if test="queryDto.endTime != null">
                AND create_time &lt;= #{queryDto.endTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>
</mapper>
