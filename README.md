# Spring Boot 2.7.5 + MyBatis Plus 项目脚手架

这是一个基于 Spring Boot 2.7.5 和 MyBatis Plus 的项目脚手架，提供了完整的用户管理功能示例、多数据源切换功能、SOAP Web Service调用功能和统一API响应格式。

## 技术栈

- **Spring Boot**: 2.7.5
- **MyBatis Plus**: 3.5.3.1
- **Dynamic Datasource**: 3.6.1 (多数据源)
- **MySQL**: 8.0.33
- **Druid**: 1.2.16 (连接池)
- **Hutool**: 5.8.37 (工具包，用于SOAP调用)
- **SpringDoc OpenAPI**: 1.6.14 (API文档)
- **Lombok**: 简化代码
- **H2**: 测试数据库
- **Maven**: 项目管理

## 项目特性

### MyBatis Plus 功能
- ✅ 分页插件
- ✅ 乐观锁插件
- ✅ 防全表更新与删除插件
- ✅ 自动填充功能（创建时间、更新时间等）
- ✅ 逻辑删除
- ✅ 代码生成器支持

### 多数据源功能
- ✅ 动态数据源切换
- ✅ 读写分离支持
- ✅ 事务管理
- ✅ @DS注解方式切换
- ✅ 编程式数据源切换
- ✅ 数据源连接池监控

### SOAP Web Service 功能 🆕
- ✅ 标准 SOAP 请求发送
- ✅ 带认证的 SOAP 请求
- ✅ WSDL 文档解析
- ✅ SOAP 响应自动解析
- ✅ 连接测试功能
- ✅ 错误处理和日志记录
- ✅ 使用 Hutool 工具包实现

### 统一API响应格式 🆕
- ✅ ApiResult<T> 泛型响应类
- ✅ 统一的状态码、消息和数据格式
- ✅ 丰富的静态工厂方法
- ✅ 完整的Swagger/OpenAPI注解
- ✅ 类型安全的响应处理
- ✅ 与SpringDoc OpenAPI 3.0完全兼容

### 项目结构
```
src/
├── main/
│   ├── java/com/example/
│   │   ├── Application.java                 # 启动类
│   │   ├── common/                          # 通用类 🆕
│   │   │   └── ApiResult.java               # 统一API响应类 🆕
│   │   ├── config/                          # 配置类
│   │   │   ├── MybatisPlusConfig.java       # MyBatis Plus配置
│   │   │   └── MetaObjectHandlerConfig.java # 自动填充配置
│   │   ├── controller/                      # 控制器
│   │   │   ├── UserController.java          # 用户控制器
│   │   │   ├── MultiDataSourceController.java # 多数据源演示控制器
│   │   │   └── SoapController.java          # SOAP请求控制器 🆕
│   │   ├── entity/                          # 实体类
│   │   │   ├── BaseEntity.java              # 基础实体
│   │   │   └── User.java                    # 用户实体
│   │   ├── mapper/                          # Mapper接口
│   │   │   └── UserMapper.java              # 用户Mapper
│   │   └── service/                         # 服务层
│   │       ├── UserService.java             # 用户服务接口
│   │       ├── MultiDataSourceDemoService.java # 多数据源演示服务接口
│   │       ├── SoapService.java             # SOAP服务类 🆕
│   │       └── impl/
│   │           ├── UserServiceImpl.java     # 用户服务实现
│   │           └── MultiDataSourceDemoServiceImpl.java # 多数据源演示服务实现
│   └── resources/
│       ├── application.yml                  # 主配置文件
│       ├── mapper/                          # Mapper XML文件
│       │   └── UserMapper.xml               # 用户Mapper XML
│       └── sql/
│           └── schema.sql                   # 数据库表结构
└── test/                                    # 测试代码
    ├── java/com/example/
    │   ├── ApplicationTests.java            # 应用测试
    │   └── service/
    │       ├── UserServiceTest.java         # 用户服务测试
    │       └── SoapServiceTest.java         # SOAP服务测试 🆕
    └── resources/
        ├── application-test.yml             # 测试配置
        └── schema.sql                       # 测试表结构
├── API_RESULT_USAGE.md                      # ApiResult使用指南 🆕
├── SOAP_API_USAGE.md                        # SOAP API使用说明
├── SOAP_IMPLEMENTATION_SUMMARY.md          # SOAP实现总结
└── SWAGGER_USAGE.md                         # Swagger使用说明
```

## 快速开始

### 1. 环境要求
- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库配置

#### 单数据源配置（默认）
1. 创建数据库：
```sql
CREATE DATABASE demo DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行表结构脚本：
```bash
mysql -u root -p demo < src/main/resources/sql/schema.sql
```

#### 多数据源配置
如果需要测试多数据源功能，需要创建多个数据库：

```sql
-- 主数据库（写库）
CREATE DATABASE demo DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 从数据库1（读库）
CREATE DATABASE demo_slave1 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 从数据库2（读库）
CREATE DATABASE demo_slave2 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 修改配置文件 `src/main/resources/application.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ********************************?...
          username: your_username
          password: your_password
        slave_1:
          url: ********************************?...
          username: your_username
          password: your_password
        slave_2:
          url: ********************************?...
          username: your_username
          password: your_password
```

### 3. 运行项目
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

### 4. 访问应用
- 应用地址：http://localhost:8080/api
- Druid监控：http://localhost:8080/api/druid (admin/123456)

## API 接口

### 用户管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/users` | 分页查询用户列表 |
| GET | `/api/users/{id}` | 根据ID查询用户 |
| GET | `/api/users/username/{username}` | 根据用户名查询用户 |
| GET | `/api/users/active` | 查询活跃用户列表 |
| POST | `/api/users` | 创建用户 |
| PUT | `/api/users/{id}` | 更新用户 |
| DELETE | `/api/users/{id}` | 删除用户 |
| DELETE | `/api/users/batch` | 批量删除用户 |
| PUT | `/api/users/{id}/password` | 重置用户密码 |

### 多数据源演示接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/datasource/master/users` | 从主数据源查询用户 |
| GET | `/api/datasource/slave1/users` | 从从数据源1查询用户 |
| GET | `/api/datasource/slave2/users` | 从从数据源2查询用户 |
| GET | `/api/datasource/dynamic/{dataSourceKey}/users` | 动态切换数据源查询用户 |
| POST | `/api/datasource/transaction/user` | 演示事务中的数据源切换 |
| POST | `/api/datasource/read-write-separation` | 演示读写分离场景 |
| GET | `/api/datasource/statistics` | 获取所有数据源的统计信息 |
| GET | `/api/datasource/test-connection/{dataSourceKey}` | 测试数据源连接 |

### SOAP Web Service 接口 🆕

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/soap/call` | 发送SOAP请求（推荐） |
| POST | `/api/soap/call-with-auth` | 发送带认证的SOAP请求 |
| GET | `/api/soap/parse-wsdl` | 解析WSDL文档 |
| GET | `/api/soap/test-connection` | 测试SOAP服务连通性 |
| GET | `/api/soap/example` | 获取SOAP请求示例 |
| POST | `/api/soap/send` | 发送简单SOAP请求（原始方法） |
| POST | `/api/soap/send-with-auth` | 发送带认证SOAP请求（原始方法） |
| GET | `/api/soap/wsdl` | 获取WSDL内容（原始方法） |

> 📖 详细的SOAP API使用说明请参考：[SOAP_API_USAGE.md](SOAP_API_USAGE.md)

### 请求示例

#### 分页查询用户
```bash
curl -X GET "http://localhost:8080/api/users?current=1&size=10&username=admin&status=0"
```

#### 创建用户
```bash
curl -X POST "http://localhost:8080/api/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "123456",
    "nickname": "新用户",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "gender": 0,
    "status": 0,
    "remark": "新创建的用户"
  }'
```

#### 多数据源演示
```bash
# 从主数据源查询用户
curl -X GET "http://localhost:8080/api/datasource/master/users"

# 从从数据源1查询用户
curl -X GET "http://localhost:8080/api/datasource/slave1/users"

# 动态切换数据源
curl -X GET "http://localhost:8080/api/datasource/dynamic/slave_2/users"

# 测试数据源连接
curl -X GET "http://localhost:8080/api/datasource/test-connection/master"

# 演示读写分离
curl -X POST "http://localhost:8080/api/datasource/read-write-separation" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456",
    "email": "<EMAIL>"
  }'
```

#### SOAP Web Service 演示 🆕
```bash
# 获取SOAP请求示例
curl -X GET "http://localhost:8080/api/soap/example"

# 发送SOAP请求（计算器服务示例）
curl -X POST "http://localhost:8080/api/soap/call" \
  -H "Content-Type: application/json" \
  -d '{
    "soapUrl": "http://www.dneonline.com/calculator.asmx",
    "soapAction": "http://tempuri.org/Add",
    "methodName": "Add",
    "namespace": "http://tempuri.org/",
    "parameters": {
      "intA": "10",
      "intB": "20"
    }
  }'

# 解析WSDL文档
curl -X GET "http://localhost:8080/api/soap/parse-wsdl?wsdlUrl=http://www.dneonline.com/calculator.asmx?WSDL"

# 测试SOAP服务连通性
curl -X GET "http://localhost:8080/api/soap/test-connection?soapUrl=http://www.dneonline.com/calculator.asmx"

# 发送带认证的SOAP请求
curl -X POST "http://localhost:8080/api/soap/call-with-auth" \
  -H "Content-Type: application/json" \
  -d '{
    "soapUrl": "http://example.com/secure-soap-service",
    "username": "your-username",
    "password": "your-password",
    "methodName": "GetSecureData",
    "namespace": "http://example.com/secure/",
    "parameters": {
      "dataId": "12345"
    }
  }'
```

## 多数据源使用说明

### 1. @DS注解方式
```java
@Service
@DS("master") // 类级别默认数据源
public class UserServiceImpl {
    
    @DS("slave_1") // 方法级别数据源，会覆盖类级别
    public User getByUsername(String username) {
        // 使用slave_1数据源
        return userMapper.selectByUsername(username);
    }
    
    @DS("master") // 写操作使用主数据源
    public boolean createUser(User user) {
        // 使用master数据源
        return save(user);
    }
}
```

### 2. 编程式切换
```java
public List<User> getUsersByDataSource(String dataSourceKey) {
    // 手动切换数据源
    DynamicDataSourceContextHolder.push(dataSourceKey);
    try {
        return userMapper.selectList(queryWrapper);
    } finally {
        // 清理数据源上下文
        DynamicDataSourceContextHolder.clear();
    }
}
```

### 3. 数据源配置
```yaml
spring:
  datasource:
    dynamic:
      primary: master  # 默认数据源
      strict: false    # 严格模式
      datasource:
        master:        # 主数据源
          url: ********************************
          username: root
          password: 123456
        slave_1:       # 从数据源1
          url: ********************************
          username: root
          password: 123456
        slave_2:       # 从数据源2
          url: ********************************
          username: root
          password: 123456
```

## 统一API响应格式 🆕

项目使用 `ApiResult<T>` 类来统一所有API接口的响应格式，具有以下优势：

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 实际数据内容
  }
}
```

### 使用示例
```java
// 成功响应（有数据）
@GetMapping("/{id}")
public ResponseEntity<ApiResult<User>> getUserById(@PathVariable Long id) {
    User user = userService.getById(id);
    if (user != null) {
        return ResponseEntity.ok(ApiResult.success("查询成功", user));
    } else {
        return ResponseEntity.ok(ApiResult.error(404, "用户不存在"));
    }
}

// 列表响应
@GetMapping("/active")
public ResponseEntity<ApiResult<List<User>>> getActiveUsers() {
    List<User> users = userService.getActiveUsers();
    return ResponseEntity.ok(ApiResult.success("查询成功", users));
}

// 分页响应
@GetMapping
public ResponseEntity<ApiResult<IPage<User>>> getUserPage(
        @RequestParam(defaultValue = "1") Integer current,
        @RequestParam(defaultValue = "10") Integer size) {
    Page<User> page = new Page<>(current, size);
    IPage<User> userPage = userService.getUserPage(page, null, null);
    return ResponseEntity.ok(ApiResult.success("查询成功", userPage));
}

// 条件响应
@PostMapping
public ResponseEntity<ApiResult<User>> createUser(@RequestBody @Valid User user) {
    boolean success = userService.createUser(user);
    return ResponseEntity.ok(ApiResult.result(success, "创建成功", "创建失败", user));
}
```

### 主要特性
- **泛型支持**：`ApiResult<T>` 支持任意数据类型
- **类型安全**：编译时类型检查，避免类型转换错误
- **统一格式**：所有接口返回格式保持一致
- **Swagger集成**：完整的API文档支持
- **便捷方法**：丰富的静态工厂方法

### 静态工厂方法
```java
// 成功响应
ApiResult.success()                           // 无数据成功
ApiResult.success(data)                       // 有数据成功
ApiResult.success("自定义消息", data)          // 自定义消息

// 失败响应
ApiResult.error("错误消息")                   // 默认400状态码
ApiResult.error(404, "用户不存在")            // 自定义状态码

// 条件响应
ApiResult.result(success, "成功", "失败")      // 根据布尔值返回
ApiResult.result(success, "成功", "失败", data) // 带数据的条件响应
```

> 📖 详细的使用说明请参考：[API_RESULT_USAGE.md](API_RESULT_USAGE.md)

## 开发建议

1. **实体类设计**：继承 `BaseEntity` 获得通用字段
2. **Mapper接口**：继承 `BaseMapper<T>` 获得基础CRUD方法
3. **服务层**：继承 `ServiceImpl<M, T>` 获得基础服务方法
4. **分页查询**：使用 `Page<T>` 进行分页
5. **条件构造**：使用 `LambdaQueryWrapper` 构造查询条件
6. **多数据源**：
   - 读操作使用从数据源（slave_1, slave_2）
   - 写操作使用主数据源（master）
   - 使用 `@DS` 注解指定数据源
   - 事务中谨慎切换数据源

## 常见问题

### 1. 数据库连接失败
检查数据库连接配置和数据库服务是否启动。

### 2. 表不存在
确保已执行数据库表结构脚本。

### 3. 分页不生效
检查是否正确配置了分页插件。

### 4. 多数据源切换失败
- 检查数据源配置是否正确
- 确认数据源key是否存在
- 查看日志中的数据源切换信息

### 5. 事务回滚问题
在多数据源环境中，跨数据源的事务需要使用分布式事务管理。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！ 